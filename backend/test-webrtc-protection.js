const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testWebRTCProtection() {
  console.log('🧪 Testing WebRTC Leak Protection...\n');
  
  const antidetectManager = new AntidetectManager();
  let browser = null;
  let page = null;

  try {
    // Load a test persona
    console.log('Loading personas...');
    await antidetectManager.loadPersonas();

    if (!antidetectManager.personas || antidetectManager.personas.length === 0) {
      throw new Error('No personas loaded');
    }

    const persona = antidetectManager.personas[0];
    console.log(`📋 Using persona: ${persona.id} (${persona.platform})`);

    // Create browser with WebRTC protection
    const launchOptions = {
      headless: false,
      devtools: false,
      ignoreDefaultArgs: ['--enable-automation'],
      args: [
        // Stealth flags
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--test-type',
        '--no-default-browser-check',
        '--no-first-run',
        
        // WebRTC leak protection flags
        '--disable-webrtc-multiple-routes',
        '--disable-webrtc-hw-decoding',
        '--disable-webrtc-hw-encoding',
        '--force-webrtc-ip-handling-policy=disable_non_proxied_udp',
        '--webrtc-ip-handling-policy=disable_non_proxied_udp',
        '--disable-features=WebRtcHideLocalIpsWithMdns',
        '--disable-webrtc',
        '--disable-rtc-smoothness-algorithm',
        '--disable-webrtc-encryption',
        '--disable-webrtc-stun-origin',
        
        // Security
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-logging',
        '--silent',
        '--log-level=3'
      ]
    };

    const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
    
    browser = await chromium.launchPersistentContext('./webrtc-test-profile', {
      ...launchOptions,
      ...contextOptions
    });

    // Apply WebRTC protection script
    await browser.addInitScript(() => {
      // Completely disable WebRTC
      console.log('🔧 Applying WebRTC protection...');

      // Method 1: Delete WebRTC constructors
      delete window.RTCPeerConnection;
      delete window.webkitRTCPeerConnection;
      delete window.mozRTCPeerConnection;
      delete window.RTCSessionDescription;
      delete window.RTCIceCandidate;

      // Method 2: Override with error-throwing functions
      window.RTCPeerConnection = function() {
        throw new Error('WebRTC is disabled for privacy');
      };

      window.webkitRTCPeerConnection = function() {
        throw new Error('WebRTC is disabled for privacy');
      };

      // Method 3: Block getUserMedia
      if (navigator.mediaDevices) {
        navigator.mediaDevices.getUserMedia = function() {
          return Promise.reject(new Error('Permission denied'));
        };
      }

      console.log('✅ WebRTC protection applied');
    });

    // Also apply the full stealth script
    const stealthScript = antidetectManager.createUltimateStealthScript(persona);
    await browser.addInitScript(stealthScript);
    
    page = await browser.newPage();
    console.log('✅ Browser launched with WebRTC protection!\n');

    // Test 1: Check if WebRTC is properly blocked
    console.log('🔍 Test 1: Checking WebRTC API availability...');
    const webrtcCheck = await page.evaluate(() => {
      return {
        hasRTCPeerConnection: typeof window.RTCPeerConnection !== 'undefined',
        hasWebkitRTCPeerConnection: typeof window.webkitRTCPeerConnection !== 'undefined',
        hasMozRTCPeerConnection: typeof window.mozRTCPeerConnection !== 'undefined',
        hasGetUserMedia: typeof navigator.mediaDevices?.getUserMedia !== 'undefined',
        hasLegacyGetUserMedia: typeof navigator.getUserMedia !== 'undefined'
      };
    });

    console.log('   RTCPeerConnection available:', webrtcCheck.hasRTCPeerConnection);
    console.log('   webkitRTCPeerConnection available:', webrtcCheck.hasWebkitRTCPeerConnection);
    console.log('   mozRTCPeerConnection available:', webrtcCheck.hasMozRTCPeerConnection);
    console.log('   getUserMedia available:', webrtcCheck.hasGetUserMedia);
    console.log('   Legacy getUserMedia available:', webrtcCheck.hasLegacyGetUserMedia);

    if (!webrtcCheck.hasRTCPeerConnection && !webrtcCheck.hasWebkitRTCPeerConnection && !webrtcCheck.hasMozRTCPeerConnection) {
      console.log('   ✅ WebRTC APIs successfully disabled!');
    } else {
      console.log('   ⚠️ Some WebRTC APIs are still available');
    }

    // Test 2: Try to create RTCPeerConnection and check for leaks
    console.log('\n🔍 Test 2: Testing RTCPeerConnection behavior...');
    const rtcTest = await page.evaluate(async () => {
      try {
        const pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ]
        });

        let candidatesReceived = 0;
        let localIPs = [];

        pc.onicecandidate = function(event) {
          candidatesReceived++;
          if (event.candidate) {
            const candidate = event.candidate.candidate;
            const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
            if (ipMatch) {
              localIPs.push(ipMatch[1]);
            }
          }
        };

        const offer = await pc.createOffer({
          offerToReceiveAudio: true,
          offerToReceiveVideo: true
        });

        await pc.setLocalDescription(offer);

        // Wait for ICE gathering
        await new Promise(resolve => setTimeout(resolve, 3000));

        return {
          success: true,
          candidatesReceived,
          localIPs: [...new Set(localIPs)],
          sdpContainsIP: offer.sdp.includes('c=IN IP4') && !offer.sdp.includes('c=IN IP4 0.0.0.0')
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          webrtcDisabled: error.message.includes('WebRTC is disabled')
        };
      }
    });

    if (rtcTest.success) {
      console.log('   ICE candidates received:', rtcTest.candidatesReceived);
      console.log('   Local IPs discovered:', rtcTest.localIPs);
      console.log('   SDP contains real IP:', rtcTest.sdpContainsIP);

      if (rtcTest.candidatesReceived === 0 && rtcTest.localIPs.length === 0 && !rtcTest.sdpContainsIP) {
        console.log('   ✅ WebRTC leak protection WORKING!');
      } else {
        console.log('   ❌ WebRTC leak protection FAILED!');
      }
    } else {
      if (rtcTest.webrtcDisabled) {
        console.log('   ✅ WebRTC completely disabled:', rtcTest.error);
      } else {
        console.log('   ❌ RTCPeerConnection test failed:', rtcTest.error);
      }
    }

    // Test 3: Navigate to browserleaks.com for real-world test
    console.log('\n🔍 Test 3: Opening browserleaks.com WebRTC test...');
    await page.goto('https://browserleaks.com/webrtc', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    console.log('✅ Opened browserleaks.com - check the results manually!');
    console.log('   Expected result: "WebRTC Leak Test ✔ No Leak"');
    console.log('   Current page URL:', page.url());
    
    // Wait for user to check results
    console.log('\n⏳ Waiting 30 seconds for manual inspection...');
    await page.waitForTimeout(30000);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
      console.log('\n🔧 Browser closed');
    }
  }
}

// Run the test
testWebRTCProtection().catch(console.error);
