#!/usr/bin/env node

/**
 * Script để tối ưu hóa antidetect system dựa trên kết quả test
 * Chạy: node scripts/optimize-antidetect.js
 */

const AntidetectManager = require('../src/antidetect/antidetect-manager');
const path = require('path');
const fs = require('fs').promises;

async function optimizeAntidetect() {
  console.log('🔧 Optimizing Antidetect System...\n');

  const antidetectManager = new AntidetectManager();
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    console.log('✅ Personas loaded successfully');

    // Test multiple personas
    const testResults = [];
    const testCount = 5;
    
    console.log(`🧪 Testing ${testCount} different personas...\n`);
    
    for (let i = 0; i < testCount; i++) {
      console.log(`--- Test ${i + 1}/${testCount} ---`);
      
      // Get random persona
      const persona = await antidetectManager.selectRandomPersona();
      console.log(`🎭 Testing persona: ${persona.id} (${persona.platform})`);
      
      // Create profile directory
      const profileDir = path.join(__dirname, '..', 'profiles', `test-${Date.now()}-${i}`);
      
      try {
        // Launch browser
        const browser = await antidetectManager.createStealthBrowser(persona, null, profileDir);
        
        if (!browser) {
          throw new Error('Failed to launch browser');
        }

        // Get page
        const pages = browser.pages();
        const page = pages.length > 0 ? pages[0] : await browser.newPage();

        // Test basic detection
        const detectionResults = await page.evaluate(() => {
          const results = {
            webdriver: navigator.webdriver,
            automation: window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect,
            plugins: navigator.plugins.length,
            languages: navigator.languages,
            platform: navigator.platform,
            userAgent: navigator.userAgent,
            hardwareConcurrency: navigator.hardwareConcurrency,
            deviceMemory: navigator.deviceMemory,
            screen: {
              width: screen.width,
              height: screen.height,
              colorDepth: screen.colorDepth
            },
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            webgl: null
          };

          // Test WebGL
          try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
              results.webgl = {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER)
              };
            }
          } catch (e) {
            results.webgl = { error: e.message };
          }

          return results;
        });

        // Test browserscan.net quickly
        console.log('   🔍 Quick browserscan.net test...');
        await page.goto('https://www.browserscan.net/', {
          waitUntil: 'networkidle',
          timeout: 15000
        });

        // Wait for scan
        await page.waitForTimeout(10000);

        // Try to extract bot detection result
        const botDetection = await page.evaluate(() => {
          // Look for bot detection indicators
          const bodyText = document.body.textContent || '';
          
          // Common bot detection phrases
          const botIndicators = [
            'Bot Detection: Yes',
            'Bot Detection: No',
            'Automation detected',
            'Human detected',
            'Bot: Yes',
            'Bot: No'
          ];

          for (const indicator of botIndicators) {
            if (bodyText.includes(indicator)) {
              return indicator;
            }
          }

          return 'Unknown';
        });

        testResults.push({
          personaId: persona.id,
          platform: persona.platform,
          region: persona.region,
          detectionResults,
          botDetection,
          success: true
        });

        console.log(`   ✅ Test completed - Bot Detection: ${botDetection}`);

        // Close browser
        await browser.close();

      } catch (error) {
        console.log(`   ❌ Test failed: ${error.message}`);
        testResults.push({
          personaId: persona.id,
          platform: persona.platform,
          region: persona.region,
          error: error.message,
          success: false
        });
      }

      // Wait between tests
      if (i < testCount - 1) {
        console.log('   ⏳ Waiting 5 seconds before next test...\n');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }

    // Analyze results
    console.log('\n📊 Test Results Analysis:');
    console.log('=========================');

    const successfulTests = testResults.filter(r => r.success);
    const failedTests = testResults.filter(r => !r.success);

    console.log(`✅ Successful tests: ${successfulTests.length}/${testCount}`);
    console.log(`❌ Failed tests: ${failedTests.length}/${testCount}`);

    if (successfulTests.length > 0) {
      console.log('\n🔍 Detection Analysis:');
      
      // Analyze bot detection results
      const botDetectionResults = {};
      successfulTests.forEach(test => {
        const result = test.botDetection || 'Unknown';
        botDetectionResults[result] = (botDetectionResults[result] || 0) + 1;
      });

      Object.entries(botDetectionResults).forEach(([result, count]) => {
        console.log(`   ${result}: ${count} times`);
      });

      // Analyze webdriver detection
      const webdriverDetected = successfulTests.filter(test => 
        test.detectionResults.webdriver !== undefined && 
        test.detectionResults.webdriver !== null
      ).length;

      // Analyze webdriver detection
      const webdriverDetected = successfulTests.filter(test =>
        test.detectionResults.webdriver !== undefined &&
        test.detectionResults.webdriver !== null
      ).length;

      console.log(`\n🤖 WebDriver Detection: ${webdriverDetected}/${successfulTests.length} tests`);

      // Platform analysis
      const platformResults = {};
      successfulTests.forEach(test => {
        platformResults[test.platform] = (platformResults[test.platform] || 0) + 1;
      });

      console.log('\n💻 Platform Performance:');
      Object.entries(platformResults).forEach(([platform, count]) => {
        console.log(`   ${platform}: ${count} successful tests`);
      });
    }

    if (failedTests.length > 0) {
      console.log('\n❌ Failed Test Analysis:');
      failedTests.forEach(test => {
        console.log(`   ${test.personaId} (${test.platform}): ${test.error}`);
      });
    }

    // Save results
    const resultsDir = path.join(__dirname, '..', 'test-results');
    await fs.mkdir(resultsDir, { recursive: true });
    
    const resultsPath = path.join(resultsDir, `antidetect-optimization-${Date.now()}.json`);
    await fs.writeFile(resultsPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      testCount,
      results: testResults,
      summary: {
        successful: successfulTests.length,
        failed: failedTests.length,
        successRate: (successfulTests.length / testCount * 100).toFixed(1) + '%'
      }
    }, null, 2));

    console.log(`\n📁 Results saved to: ${resultsPath}`);

    // Recommendations
    console.log('\n💡 Optimization Recommendations:');
    console.log('================================');

    if (successfulTests.length === testCount) {
      console.log('🎉 Perfect! All tests passed. Antidetect system is working well.');
    } else if (successfulTests.length >= testCount * 0.8) {
      console.log('✅ Good performance. Minor optimizations may be needed.');
    } else if (successfulTests.length >= testCount * 0.5) {
      console.log('⚠️  Moderate performance. Significant optimizations needed.');
    } else {
      console.log('❌ Poor performance. Major antidetect improvements required.');
    }

    if (webdriverDetected > 0) {
      console.log('🔧 WebDriver still detected - enhance webdriver removal scripts');
    }

    console.log('\n🔧 Next steps:');
    console.log('1. Review failed tests and adjust persona configurations');
    console.log('2. Enhance stealth scripts based on detection patterns');
    console.log('3. Test with different browser versions and flags');
    console.log('4. Consider using residential proxies for better anonymity');

  } catch (error) {
    console.error('❌ Optimization failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Optimization interrupted by user');
  process.exit(0);
});

// Run the optimization
if (require.main === module) {
  optimizeAntidetect()
    .then(() => {
      console.log('\n✅ Optimization completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Optimization failed:', error.message);
      process.exit(1);
    });
}

module.exports = { optimizeAntidetect };
