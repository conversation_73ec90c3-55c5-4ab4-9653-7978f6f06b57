#!/usr/bin/env node

/**
 * Script để tạo lại personas với cải tiến 2025
 * Chạy: node scripts/regenerate-personas-2025.js
 */

const PersonaGenerator = require('../src/antidetect/persona-generator');
const fs = require('fs').promises;
const path = require('path');

async function regeneratePersonas() {
  console.log('🔄 Regenerating personas with 2025 enhancements...\n');

  try {
    const generator = new PersonaGenerator();
    
    // Generate enhanced personas
    console.log('🎭 Generating 200 enhanced personas...');
    const basePersonas = generator.generatePersonaBank(200, ['US', 'CA', 'GB', 'DE', 'FR', 'AU', 'JP', 'KR']);

    // Enhance each persona with 2025 features
    const personas = basePersonas.map((persona, i) => {
      // Add enhanced properties for 2025
      persona.canvasSeed = Math.floor(Math.random() * 100000) + 10000;
      persona.audioSeed = Math.floor(Math.random() * 100000) + 10000;
      persona.performanceVariance = Math.random() * 0.2 + 0.1;

      // Enhanced WebGL with more realistic values
      if (persona.webgl) {
        // Add more realistic WebGL parameters
        persona.webgl.maxTextureSize = [4096, 8192, 16384][Math.floor(Math.random() * 3)];
        persona.webgl.maxViewportDims = [4096, 8192, 16384][Math.floor(Math.random() * 3)];
        persona.webgl.maxVertexAttribs = 16 + Math.floor(Math.random() * 16);
        persona.webgl.maxVertexUniformVectors = 256 + Math.floor(Math.random() * 768);
        persona.webgl.maxFragmentUniformVectors = 256 + Math.floor(Math.random() * 768);
        persona.webgl.maxVaryingVectors = 8 + Math.floor(Math.random() * 24);
      }

      // Enhanced screen properties
      persona.screenColorDepth = 24;
      persona.screenPixelDepth = 24;
      persona.screenAvailLeft = 0;
      persona.screenAvailTop = 0;

      // Enhanced connection properties
      persona.connection = {
        effectiveType: ['4g', '3g', 'slow-2g'][Math.floor(Math.random() * 3)],
        downlink: Math.random() * 10 + 1,
        rtt: Math.floor(Math.random() * 100) + 50
      };

      // Enhanced battery properties (if mobile)
      if (persona.isMobile) {
        persona.battery = {
          charging: Math.random() > 0.5,
          level: Math.random() * 0.8 + 0.2,
          chargingTime: Math.random() > 0.5 ? Infinity : Math.floor(Math.random() * 7200),
          dischargingTime: Math.floor(Math.random() * 28800) + 3600
        };
      }

      if ((i + 1) % 50 === 0) {
        console.log(`   Enhanced ${i + 1}/200 personas...`);
      }

      return persona;
    });

    // Save personas
    const dataDir = path.join(__dirname, '..', 'data');
    await fs.mkdir(dataDir, { recursive: true });
    
    const personasPath = path.join(dataDir, 'personas.json');
    await fs.writeFile(personasPath, JSON.stringify(personas, null, 2));
    
    console.log(`✅ Generated ${personas.length} personas saved to: ${personasPath}`);

    // Generate statistics
    const stats = {
      total: personas.length,
      platforms: {},
      browsers: {},
      screenResolutions: {},
      webglVendors: {},
      timezones: {}
    };

    personas.forEach(persona => {
      // Platform stats
      stats.platforms[persona.platform] = (stats.platforms[persona.platform] || 0) + 1;
      
      // Browser stats
      const browser = persona.userAgent.includes('Chrome') ? 'Chrome' : 
                     persona.userAgent.includes('Firefox') ? 'Firefox' :
                     persona.userAgent.includes('Safari') ? 'Safari' : 'Other';
      stats.browsers[browser] = (stats.browsers[browser] || 0) + 1;
      
      // Screen resolution stats
      const resolution = `${persona.screenWidth}x${persona.screenHeight}`;
      stats.screenResolutions[resolution] = (stats.screenResolutions[resolution] || 0) + 1;
      
      // WebGL vendor stats
      if (persona.webgl) {
        stats.webglVendors[persona.webgl.vendor] = (stats.webglVendors[persona.webgl.vendor] || 0) + 1;
      }
      
      // Timezone stats
      stats.timezones[persona.timezone] = (stats.timezones[persona.timezone] || 0) + 1;
    });

    console.log('\n📊 Persona Statistics:');
    console.log('======================');
    console.log(`Total personas: ${stats.total}`);
    
    console.log('\nPlatforms:');
    Object.entries(stats.platforms).forEach(([platform, count]) => {
      console.log(`  ${platform}: ${count} (${(count/stats.total*100).toFixed(1)}%)`);
    });
    
    console.log('\nBrowsers:');
    Object.entries(stats.browsers).forEach(([browser, count]) => {
      console.log(`  ${browser}: ${count} (${(count/stats.total*100).toFixed(1)}%)`);
    });
    
    console.log('\nTop Screen Resolutions:');
    Object.entries(stats.screenResolutions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .forEach(([resolution, count]) => {
        console.log(`  ${resolution}: ${count} (${(count/stats.total*100).toFixed(1)}%)`);
      });
    
    console.log('\nWebGL Vendors:');
    Object.entries(stats.webglVendors).forEach(([vendor, count]) => {
      console.log(`  ${vendor}: ${count} (${(count/stats.total*100).toFixed(1)}%)`);
    });
    
    console.log('\nTop Timezones:');
    Object.entries(stats.timezones)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .forEach(([timezone, count]) => {
        console.log(`  ${timezone}: ${count} (${(count/stats.total*100).toFixed(1)}%)`);
      });

    // Save statistics
    const statsPath = path.join(dataDir, 'persona-stats.json');
    await fs.writeFile(statsPath, JSON.stringify(stats, null, 2));
    console.log(`\n📈 Statistics saved to: ${statsPath}`);

    console.log('\n✅ Persona regeneration completed successfully!');
    console.log('\n🔧 Next steps:');
    console.log('1. Run: node test-antidetect-browserscan.js');
    console.log('2. Check browser detection results');
    console.log('3. Adjust antidetect settings if needed');

  } catch (error) {
    console.error('❌ Error regenerating personas:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  regeneratePersonas()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { regeneratePersonas };
