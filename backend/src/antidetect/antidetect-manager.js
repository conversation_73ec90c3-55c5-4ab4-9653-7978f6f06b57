/**
 * Antidetect Manager
 * <PERSON>u<PERSON>n lý việc áp dụng personas vào browser contexts
 */

const path = require('path');
const fs = require('fs').promises;
const PersonaGenerator = require('./persona-generator');
const GoogleEvasion = require('./google-evasion');
const { TIMEZONE_BY_REGION, LANGUAGE_BY_REGION } = require('./fingerprint-data');

class AntidetectManager {
  constructor() {
    this.personas = [];
    this.personasLoaded = false;
    this.personaGenerator = new PersonaGenerator();
    this.googleEvasion = new GoogleEvasion();
  }

  /**
   * Load personas từ file
   * @returns {Promise<void>}
   */
  async loadPersonas() {
    if (this.personasLoaded) return;

    try {
      const personasPath = path.join(__dirname, '../../data/personas.json');
      console.log(`Loading personas from: ${personasPath}`);
      const content = await fs.readFile(personasPath, 'utf-8');
      const data = JSON.parse(content);

      // Check if data is array directly or wrapped in object
      if (Array.isArray(data)) {
        this.personas = data;
      } else if (data.personas && Array.isArray(data.personas)) {
        this.personas = data.personas;
      } else {
        throw new Error('Invalid personas data format');
      }

      this.personasLoaded = true;
      console.log(`✅ Successfully loaded ${this.personas.length} personas`);

      // Log first persona for verification
      if (this.personas.length > 0) {
        console.log(`First persona: ${this.personas[0].id} (${this.personas[0].platform})`);
      }
    } catch (error) {
      console.error('❌ Error loading personas:', error.message);
      // Fallback: tạo một số personas mặc định
      this.personas = [
        this.personaGenerator.generateWindowsPersona('US'),
        this.personaGenerator.generateMacPersona('US'),
        this.personaGenerator.generateWindowsPersona('GB'),
        this.personaGenerator.generateMacPersona('CA')
      ];
      this.personasLoaded = true;
      console.log(`⚠️  Using ${this.personas.length} fallback personas`);
    }
  }

  /**
   * Lựa chọn persona ngẫu nhiên
   * @param {string} preferredRegion - Region ưu tiên (optional)
   * @returns {Object} - Persona object
   */
  async selectRandomPersona(preferredRegion = null) {
    await this.loadPersonas();

    console.log(`🎭 Selecting persona from ${this.personas.length} available personas`);
    if (preferredRegion) {
      console.log(`🌍 Preferred region: ${preferredRegion}`);
    }

    let availablePersonas = this.personas;

    // Nếu có region ưu tiên, ưu tiên chọn persona từ region đó
    if (preferredRegion) {
      const regionPersonas = this.personas.filter(p => p.region === preferredRegion);
      console.log(`Found ${regionPersonas.length} personas for region ${preferredRegion}`);
      if (regionPersonas.length > 0) {
        availablePersonas = regionPersonas;
      } else {
        console.log(`⚠️  No personas found for region ${preferredRegion}, using all available personas`);
      }
    }

    if (availablePersonas.length === 0) {
      console.error('❌ No personas available after filtering');
      console.error(`Total personas loaded: ${this.personas.length}`);
      console.error(`Personas loaded status: ${this.personasLoaded}`);
      throw new Error(`No personas available. Total loaded: ${this.personas.length}, Preferred region: ${preferredRegion || 'any'}`);
    }

    const randomIndex = Math.floor(Math.random() * availablePersonas.length);
    const selectedPersona = availablePersonas[randomIndex];
    console.log(`✅ Selected persona: ${selectedPersona.id} (${selectedPersona.platform}, ${selectedPersona.region})`);
    return selectedPersona;
  }

  /**
   * Ánh xạ từ proxy location đến region code
   * @param {string} country - Mã quốc gia từ proxy
   * @returns {string} - Region code
   */
  mapCountryToRegion(country) {
    const mapping = {
      'US': 'US',
      'CA': 'CA', 
      'GB': 'GB',
      'UK': 'GB',
      'DE': 'DE',
      'FR': 'FR',
      'IT': 'IT',
      'ES': 'ES',
      'AU': 'AU',
      'JP': 'JP',
      'KR': 'KR',
      'CN': 'CN',
      'IN': 'IN',
      'BR': 'BR',
      'MX': 'MX'
    };

    return mapping[country?.toUpperCase()] || 'US';
  }

  /**
   * Tạo timezone và locale từ proxy location với enhanced consistency
   * @param {Object} proxy - Proxy object với country và city
   * @returns {Object} - { timezone, locale, languages }
   */
  generateLocaleFromProxy(proxy) {
    if (!proxy || !proxy.country) {
      return {
        timezone: 'America/New_York',
        locale: 'en-US',
        languages: ['en-US', 'en'],
        country: 'US',
        region: 'US'
      };
    }

    const region = this.mapCountryToRegion(proxy.country);
    const timezones = TIMEZONE_BY_REGION[region] || TIMEZONE_BY_REGION['US'];
    const language = LANGUAGE_BY_REGION[region] || LANGUAGE_BY_REGION['US'];

    // Enhanced timezone selection based on city if available
    let timezone;
    if (proxy.city) {
      timezone = this.getCityTimezone(proxy.city, proxy.country);
    }
    if (!timezone) {
      timezone = timezones[Math.floor(Math.random() * timezones.length)];
    }

    return {
      timezone,
      locale: language.primary,
      languages: [language.primary, ...language.secondary],
      country: proxy.country,
      region
    };
  }

  /**
   * Get timezone for specific city
   * @param {string} city - City name
   * @param {string} country - Country code
   * @returns {string|null} - Timezone or null if not found
   */
  getCityTimezone(city, country) {
    const cityTimezones = {
      'US': {
        'New York': 'America/New_York',
        'Los Angeles': 'America/Los_Angeles',
        'Chicago': 'America/Chicago',
        'Houston': 'America/Chicago',
        'Phoenix': 'America/Phoenix',
        'Philadelphia': 'America/New_York',
        'San Antonio': 'America/Chicago',
        'San Diego': 'America/Los_Angeles',
        'Dallas': 'America/Chicago',
        'San Jose': 'America/Los_Angeles',
        'Austin': 'America/Chicago',
        'Seattle': 'America/Los_Angeles',
        'Denver': 'America/Denver',
        'Washington': 'America/New_York',
        'Boston': 'America/New_York',
        'Miami': 'America/New_York',
        'Atlanta': 'America/New_York',
        'Las Vegas': 'America/Los_Angeles',
        'Portland': 'America/Los_Angeles',
        'San Francisco': 'America/Los_Angeles'
      },
      'GB': {
        'London': 'Europe/London',
        'Birmingham': 'Europe/London',
        'Manchester': 'Europe/London',
        'Glasgow': 'Europe/London',
        'Liverpool': 'Europe/London',
        'Leeds': 'Europe/London',
        'Edinburgh': 'Europe/London',
        'Bristol': 'Europe/London',
        'Cardiff': 'Europe/London'
      },
      'DE': {
        'Berlin': 'Europe/Berlin',
        'Hamburg': 'Europe/Berlin',
        'Munich': 'Europe/Berlin',
        'Cologne': 'Europe/Berlin',
        'Frankfurt': 'Europe/Berlin',
        'Stuttgart': 'Europe/Berlin'
      },
      'FR': {
        'Paris': 'Europe/Paris',
        'Marseille': 'Europe/Paris',
        'Lyon': 'Europe/Paris',
        'Toulouse': 'Europe/Paris',
        'Nice': 'Europe/Paris',
        'Bordeaux': 'Europe/Paris'
      },
      'CA': {
        'Toronto': 'America/Toronto',
        'Montreal': 'America/Montreal',
        'Vancouver': 'America/Vancouver',
        'Calgary': 'America/Edmonton',
        'Edmonton': 'America/Edmonton',
        'Ottawa': 'America/Toronto'
      },
      'AU': {
        'Sydney': 'Australia/Sydney',
        'Melbourne': 'Australia/Melbourne',
        'Brisbane': 'Australia/Brisbane',
        'Perth': 'Australia/Perth',
        'Adelaide': 'Australia/Adelaide'
      }
    };

    const countryTimezones = cityTimezones[country];
    if (!countryTimezones) return null;

    // Try exact match first
    if (countryTimezones[city]) {
      return countryTimezones[city];
    }

    // Try case-insensitive match
    const cityLower = city.toLowerCase();
    for (const [cityName, timezone] of Object.entries(countryTimezones)) {
      if (cityName.toLowerCase() === cityLower) {
        return timezone;
      }
    }

    // Try partial match
    for (const [cityName, timezone] of Object.entries(countryTimezones)) {
      if (cityName.toLowerCase().includes(cityLower) || cityLower.includes(cityName.toLowerCase())) {
        return timezone;
      }
    }

    return null;
  }

  /**
   * Tạo geolocation từ proxy
   * @param {Object} proxy - Proxy object
   * @returns {Object} - Geolocation object
   */
  generateGeolocationFromProxy(proxy) {
    if (!proxy || !proxy.country) {
      return {
        latitude: 40.7128,
        longitude: -74.0060,
        accuracy: 100
      };
    }

    // Sử dụng persona generator để tạo geolocation
    const region = this.mapCountryToRegion(proxy.country);
    return this.personaGenerator.generateGeolocation(region);
  }

  /**
   * Tạo browser context options từ persona và proxy
   * @param {Object} persona - Persona object
   * @param {Object} proxy - Proxy object (optional)
   * @param {string} accountId - Account ID for bridge management (optional)
   * @returns {Promise<Object>} - Playwright context options
   */
  async createContextOptions(persona, proxy = null, accountId = null) {
    const localeInfo = proxy ? this.generateLocaleFromProxy(proxy) : {
      timezone: persona.timezone,
      locale: persona.language,
      languages: persona.languages
    };

    const geolocation = proxy ? this.generateGeolocationFromProxy(proxy) : persona.geolocation;

    const options = {
      userAgent: persona.userAgent,
      viewport: {
        width: persona.screen.width,
        height: persona.screen.height
      },
      screen: {
        width: persona.screen.width,
        height: persona.screen.height
      },
      locale: localeInfo.locale,
      timezoneId: localeInfo.timezone,
      geolocation: {
        latitude: geolocation.latitude,
        longitude: geolocation.longitude,
        accuracy: geolocation.accuracy
      },
      permissions: ['geolocation'],
      deviceScaleFactor: 1,
      isMobile: false,
      hasTouch: false,
      colorScheme: 'light',
      reducedMotion: 'no-preference',
      forcedColors: 'none',
      extraHTTPHeaders: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': localeInfo.languages.map((lang, index) => {
          const quality = index === 0 ? '' : `;q=${(0.9 - index * 0.1).toFixed(1)}`;
          return `${lang}${quality}`;
        }).join(','),
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'max-age=0',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'sec-ch-ua': `"Not_A Brand";v="8", "Chromium";v="${this.extractChromeVersion(persona.userAgent)}", "Google Chrome";v="${this.extractChromeVersion(persona.userAgent)}"`,
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': `"${persona.platform === 'Windows' ? 'Windows' : 'macOS'}"`,
        'DNT': '1'
      }
    };

    // Thêm proxy nếu có
    if (proxy && proxy.type !== 'No proxy') {
      try {
        console.log(`🌐 Configuring proxy: ${proxy.type} ${proxy.host}:${proxy.port}`);
        const ProxyService = require('../services/ProxyService');
        const proxyService = new ProxyService();

        // Kiểm tra loại proxy và xử lý phù hợp
        const proxyType = proxy.type.toLowerCase();

        if (proxyType === 'socks5' && proxy.username && proxy.password) {
          // SOCKS5 với auth: Sử dụng bridge
          console.log(`🌉 SOCKS5 with auth detected - using bridge approach`);

          if (accountId) {
            options.proxy = await proxyService.createProxyConfigWithBridge(proxy, accountId);
            console.log(`✅ SOCKS5 bridge configured: ${options.proxy.server}`);
          } else {
            console.log(`❌ Warning: No accountId provided for SOCKS5 bridge setup`);
            console.log(`   Skipping proxy configuration - direct connection will be used`);
            console.log(`   Consider providing accountId for SOCKS5 bridge support`);
            // Don't set options.proxy - use direct connection
          }
        } else {
          // Các loại proxy khác (HTTP, HTTPS, SOCKS5 không auth)
          options.proxy = proxyService.createProxyConfig(proxy);
          console.log(`✅ Proxy configured: ${options.proxy.server}`);

          // Thêm thông tin auth nếu có
          if (options.proxy.username) {
            console.log(`   Authentication: ${options.proxy.username}:***`);
          }
        }
      } catch (error) {
        console.error(`❌ Error configuring proxy: ${error.message}`);

        // Xử lý lỗi cụ thể
        if (error.message.includes('socks5 proxy authentication')) {
          console.log(`   SOCKS5 auth not supported - continuing without proxy`);
          console.log(`   Recommendation: Use HTTP/HTTPS proxy instead`);
        } else if (error.message.includes('Invalid proxy configuration')) {
          console.log(`   Invalid proxy config - check host/port/credentials`);
          throw error;
        } else {
          console.log(`   Unexpected proxy error - continuing without proxy`);
        }
      }
    } else {
      console.log(`🌐 No proxy configured - using direct connection`);
    }

    return options;
  }

  /**
   * Apply Google-specific evasion techniques to page
   * @param {Page} page - Playwright page object
   * @param {Object} persona - Persona object
   */
  async applyGoogleEvasion(page, persona) {
    try {
      await this.googleEvasion.injectGoogleEvasion(page, persona);
      await this.googleEvasion.simulateHumanPatterns(page);
      console.log('🔒 Google evasion techniques applied successfully');
    } catch (error) {
      console.error('❌ Error applying Google evasion:', error.message);
    }
  }

  /**
   * Apply antidetect techniques to page
   * @param {Page} page - Playwright page object
   * @param {Object} persona - Persona object
   */
  async applyAntidetectToPage(page, persona) {
    try {
      // Apply spoofing script
      const spoofingScript = this.createSpoofingScript(persona);
      await page.addInitScript(spoofingScript);
      console.log('✅ Antidetect spoofing script applied');
    } catch (error) {
      console.error('❌ Error applying antidetect to page:', error.message);
    }
  }

  /**
   * Add human-like delay before important actions
   * @param {number} baseDelay - Base delay in milliseconds
   */
  async addHumanDelay(baseDelay = 1000) {
    return this.googleEvasion.addHumanDelay(baseDelay);
  }

  /**
   * Tạo browser launch options với antidetect (Enhanced 2025 - TikTok Optimized)
   * @returns {Object} - Browser launch options
   */
  createBrowserLaunchOptions() {
    return {
      args: [
        // === CRITICAL AUTOMATION DETECTION BYPASS ===
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--disable-dev-shm-usage',

        // === ADVANCED WEBDRIVER DETECTION BYPASS ===
        '--disable-features=VizDisplayCompositor',
        '--disable-features=TranslateUI',
        '--disable-features=ScriptStreaming',
        '--disable-features=V8OptimizeJavascript',
        '--disable-features=VizServiceSharedBitmapManager',
        '--disable-features=MediaRouter',
        '--disable-features=OptimizationHints',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-features=BlinkGenPropertyTrees',
        '--disable-features=UserAgentClientHint',
        '--disable-features=WebPayments',
        '--disable-features=WebUSB',
        '--disable-features=WebBluetooth',
        '--disable-features=DialMediaRouteProvider',
        '--disable-features=CastMediaRouteProvider',

        // === WEBRTC LEAK PROTECTION ===
        '--disable-features=WebRtcHideLocalIpsWithMdns',
        '--disable-webrtc-multiple-routes',
        '--disable-webrtc-hw-decoding',
        '--disable-webrtc-hw-encoding',
        '--force-webrtc-ip-handling-policy=disable_non_proxied_udp',
        '--webrtc-ip-handling-policy=disable_non_proxied_udp',
        '--disable-webrtc',
        '--disable-rtc-smoothness-algorithm',
        '--disable-webrtc-encryption',
        '--disable-webrtc-stun-origin',

        // === TIKTOK-SPECIFIC OPTIMIZATIONS ===
        '--disable-features=MediaFoundationVideoCapture',
        '--disable-features=HardwareMediaKeyHandling',
        '--disable-features=MediaSessionService',
        '--disable-features=GlobalMediaControls',
        '--disable-features=PictureInPicture',
        '--disable-features=MediaRemoting',
        '--disable-features=AutoplayIgnoreWebAudio',
        '--disable-features=AutoplayPolicy',

        // === PERFORMANCE & STABILITY ===
        '--no-sandbox',
        '--no-first-run',
        '--no-default-browser-check',
        '--no-pings',
        '--no-zygote',
        '--disable-gpu-sandbox',
        '--disable-software-rasterizer',
        '--disable-gpu-watchdog',
        '--disable-gpu-process-crash-limit',

        // === BACKGROUND PROCESS OPTIMIZATION ===
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-background-networking',
        '--disable-ipc-flooding-protection',
        '--disable-field-trial-config',
        '--disable-back-forward-cache',

        // === PRIVACY & TRACKING BYPASS ===
        '--disable-client-side-phishing-detection',
        '--disable-component-extensions-with-background-pages',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-sync',
        '--disable-domain-reliability',
        '--disable-component-update',
        '--disable-background-downloads',
        '--disable-add-to-shelf',
        '--disable-breakpad',
        '--disable-crash-reporter',
        '--disable-extensions-file-access-check',
        '--disable-extensions-http-throttling',
        '--disable-login-animations',

        // === EXTENSION & PLUGIN MANAGEMENT ===
        '--disable-extensions',
        '--disable-plugins',
        '--disable-plugins-discovery',
        '--disable-default-apps',
        '--disable-translate',
        '--disable-popup-blocking',

        // === LOGGING & TELEMETRY SUPPRESSION ===
        '--disable-logging',
        '--silent',
        '--log-level=3',
        '--disable-metrics',
        '--disable-metrics-reporting',
        '--metrics-recording-only',
        '--password-store=basic',
        '--use-mock-keychain',

        // === ADVANCED STEALTH FLAGS (2025) ===
        '--disable-web-security',
        '--disable-site-isolation-trials',
        '--disable-features=VizHitTestSurfaceLayer',
        '--disable-features=VizHitTestDrawQuad',
        '--disable-features=TranslateSubFrames',
        '--disable-features=LangClientHintHeader',
        '--disable-features=AvoidUnnecessaryBeforeUnloadCheckSync',
        '--disable-features=LogJsConsoleMessages',
        '--disable-features=ChromeWhatsNewUI',
        '--disable-features=HttpsUpgrades',
        '--disable-features=PaintHolding',

        // === CRITICAL BOT DETECTION BYPASS FLAGS ===
        '--disable-features=AutomationControlled',
        '--disable-component-extensions-with-background-pages',
        '--disable-default-apps',
        '--disable-extensions-except',
        '--disable-ipc-flooding-protection',
        '--enable-features=NetworkService,NetworkServiceLogging',
        '--force-color-profile=srgb',
        '--disable-features=VizDisplayCompositor',

        // === ADDITIONAL 2025 STEALTH FLAGS ===
        '--disable-features=MediaRouter',
        '--disable-features=OptimizationHints',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-field-trial-config',
        '--disable-back-forward-cache',
        '--disable-crash-reporter',
        '--disable-extensions-file-access-check',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-features=TranslateUI',
        '--disable-features=BlinkGenPropertyTrees',

        // === MEMORY & RESOURCE OPTIMIZATION ===
        '--max_old_space_size=4096',
        '--memory-pressure-off',
        '--disable-background-media-suspend',
        '--disable-low-res-tiling',
        '--disable-partial-raster',
        '--disable-rgbaa-msaa',
        '--disable-2d-canvas-clip-aa',
        '--disable-gl-drawing-for-tests',

        // === NETWORK OPTIMIZATION ===
        '--aggressive-cache-discard',
        '--disable-background-networking',
        '--disable-default-apps',
        '--disable-extensions-http-throttling',
        '--disable-preconnect',

        // === FINAL STEALTH TOUCHES ===
        '--disable-dev-tools',
        '--disable-features=VizDisplayCompositor',
        '--use-gl=swiftshader',
        '--disable-accelerated-2d-canvas',
        '--disable-accelerated-jpeg-decoding',
        '--disable-accelerated-mjpeg-decode',
        '--disable-accelerated-video-decode'
      ]
    };
  }

  /**
   * Extract Chrome version from User-Agent
   * @param {string} userAgent - User-Agent string
   * @returns {string} - Chrome version
   */
  extractChromeVersion(userAgent) {
    const match = userAgent.match(/Chrome\/(\d+)/);
    return match ? match[1] : '122';
  }

  /**
   * Lấy persona ngẫu nhiên
   * @returns {Object|null} - Random persona object hoặc null
   */
  getRandomPersona() {
    if (!this.personasLoaded || this.personas.length === 0) {
      console.warn('⚠️  Personas not loaded yet');
      return null;
    }
    const randomIndex = Math.floor(Math.random() * this.personas.length);
    return this.personas[randomIndex];
  }

  /**
   * Lấy persona theo ID
   * @param {string} personaId - ID của persona
   * @returns {Object|null} - Persona object hoặc null
   */
  async getPersonaById(personaId) {
    await this.loadPersonas();
    return this.personas.find(p => p.id === personaId) || null;
  }

  /**
   * Tạo script spoofing cho persona (Enhanced 2025 - TikTok Optimized)
   * @param {Object} persona - Persona object
   * @returns {string} - JavaScript code để inject
   */
  createSpoofingScript(persona) {
    return `
      // === CRITICAL WEBDRIVER DETECTION BYPASS ===
      (function() {
        // Remove webdriver property completely
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
          configurable: true
        });

        // Remove ALL automation detection properties
        const automationProps = [
          '__webdriver_script_fn', '__webdriver_evaluate', '__webdriver_unwrapped',
          '__fxdriver_evaluate', '__fxdriver_unwrapped', '__driver_evaluate',
          '__selenium_evaluate', '__selenium_unwrapped', '__driver_unwrapped',
          '__webdriver_script_function', '__webdriver_script_func', '__webdriver_script',
          '__selenium_unwrapped', '__fxdriver_unwrapped', '__driver_unwrapped',
          '__webdriver_evaluate', '__selenium_evaluate', '__webdriver_script_fn',
          'webdriver', '_Selenium_IDE_Recorder', '_selenium', 'calledSelenium',
          '_WEBDRIVER_ELEM_CACHE', 'ChromeDriverw', 'driver-evaluate',
          'webdriver-evaluate', 'selenium-evaluate', 'webdriverCommand',
          'webdriver-evaluate-response', '__webdriverFunc', '__webdriver_script_function',
          '__$webdriverAsyncExecutor', '__lastWatirAlert', '__lastWatirConfirm',
          '__lastWatirPrompt', '_WEBDRIVER_ELEM_CACHE'
        ];

        automationProps.forEach(prop => {
          try {
            delete navigator[prop];
            delete window[prop];
            delete document[prop];
          } catch (e) {}
        });

        // Override webdriver property with getter that always returns undefined
        try {
          Object.defineProperty(Object.getPrototypeOf(navigator), 'webdriver', {
            set: undefined,
            enumerable: false,
            configurable: true,
            get: new Proxy(() => undefined, {
              apply: () => undefined
            })
          });
        } catch (e) {}
      })();

      // === ENHANCED CHROME RUNTIME SPOOFING ===
      (function() {
        // Create realistic chrome object with all expected properties
        const chromeRuntime = {
          onConnect: undefined,
          onMessage: undefined,
          onStartup: undefined,
          onInstalled: undefined,
          onSuspend: undefined,
          onSuspendCanceled: undefined,
          onUpdateAvailable: undefined,
          onBrowserUpdateAvailable: undefined,
          onRestartRequired: undefined,
          onConnectExternal: undefined,
          onMessageExternal: undefined,
          getBackgroundPage: () => undefined,
          getManifest: () => ({
            name: 'Chrome',
            version: '122.0.6261.94',
            manifest_version: 3
          }),
          getURL: (path) => 'chrome-extension://invalid/' + path,
          reload: () => {},
          requestUpdateCheck: () => Promise.resolve(['no_update', undefined]),
          restart: () => {},
          sendMessage: () => Promise.resolve(),
          sendNativeMessage: () => Promise.resolve(),
          setUninstallURL: () => {},
          openOptionsPage: () => Promise.resolve(),
          getPlatformInfo: () => Promise.resolve({
            os: '${persona.platform === 'Windows' ? 'win' : 'mac'}',
            arch: 'x86-64',
            nacl_arch: 'x86-64'
          })
        };

        // Create complete chrome object
        if (!window.chrome) {
          window.chrome = {};
        }

        Object.defineProperty(window.chrome, 'runtime', {
          value: chromeRuntime,
          writable: false,
          enumerable: true,
          configurable: false
        });

        // Add other chrome APIs
        window.chrome.csi = () => ({
          startE: Date.now(),
          onloadT: Date.now(),
          pageT: Math.random() * 1000 + 1000,
          tran: 15
        });

        window.chrome.loadTimes = () => ({
          requestTime: Date.now() / 1000,
          startLoadTime: Date.now() / 1000,
          commitLoadTime: Date.now() / 1000,
          finishDocumentLoadTime: Date.now() / 1000,
          finishLoadTime: Date.now() / 1000,
          firstPaintTime: Date.now() / 1000,
          firstPaintAfterLoadTime: 0,
          navigationType: 'Other',
          wasFetchedViaSpdy: false,
          wasNpnNegotiated: false,
          npnNegotiatedProtocol: '',
          wasAlternateProtocolAvailable: false,
          connectionInfo: 'http/1.1'
        });

        // Add app property
        window.chrome.app = {
          isInstalled: false,
          InstallState: {
            DISABLED: 'disabled',
            INSTALLED: 'installed',
            NOT_INSTALLED: 'not_installed'
          },
          RunningState: {
            CANNOT_RUN: 'cannot_run',
            READY_TO_RUN: 'ready_to_run',
            RUNNING: 'running'
          }
        };
      })();

      // Navigator properties spoofing
      (function() {
        // Platform spoofing
        Object.defineProperty(navigator, 'platform', {
          get: () => '${persona.platform === 'Windows' ? 'Win32' : 'MacIntel'}',
          configurable: true
        });

        // Languages spoofing
        Object.defineProperty(navigator, 'languages', {
          get: () => ${JSON.stringify(persona.languages)},
          configurable: true
        });

        // Hardware concurrency
        Object.defineProperty(navigator, 'hardwareConcurrency', {
          get: () => ${persona.hardware.hardwareConcurrency},
          configurable: true
        });

        // Device memory
        Object.defineProperty(navigator, 'deviceMemory', {
          get: () => ${persona.hardware.deviceMemory},
          configurable: true
        });

        // User agent
        Object.defineProperty(navigator, 'userAgent', {
          get: () => '${persona.userAgent}',
          configurable: true
        });

        // App version
        Object.defineProperty(navigator, 'appVersion', {
          get: () => '${persona.userAgent.substring(persona.userAgent.indexOf('/') + 1)}',
          configurable: true
        });
      })();

      // WebGL Spoofing (Enhanced with more parameters)
      (function() {
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
          if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
            return '${persona.webgl.vendor}';
          }
          if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
            return '${persona.webgl.renderer}';
          }
          if (parameter === 7936) { // VERSION
            return '${persona.webgl.version}';
          }
          if (parameter === 35724) { // SHADING_LANGUAGE_VERSION
            return '${persona.webgl.shadingLanguageVersion}';
          }
          if (parameter === 3379) { // MAX_TEXTURE_SIZE
            return ${persona.webgl.maxTextureSize || 16384};
          }
          if (parameter === 34921) { // MAX_VERTEX_ATTRIBS
            return ${persona.webgl.maxVertexAttribs || 16};
          }
          if (parameter === 36348) { // MAX_VARYING_VECTORS
            return ${persona.webgl.maxVaryingVectors || 30};
          }
          if (parameter === 33901) { // ALIASED_LINE_WIDTH_RANGE
            return new Float32Array([${persona.webgl.aliasedLineWidthRange ? persona.webgl.aliasedLineWidthRange.join(',') : '1,1'}]);
          }
          if (parameter === 33902) { // ALIASED_POINT_SIZE_RANGE
            return new Float32Array([${persona.webgl.aliasedPointSizeRange ? persona.webgl.aliasedPointSizeRange.join(',') : '1,1024'}]);
          }
          return getParameter.call(this, parameter);
        };

        if (window.WebGL2RenderingContext) {
          const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
          WebGL2RenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) {
              return '${persona.webgl.vendor}';
            }
            if (parameter === 37446) {
              return '${persona.webgl.renderer}';
            }
            if (parameter === 7936) {
              return '${persona.webgl.version}';
            }
            if (parameter === 35724) {
              return '${persona.webgl.shadingLanguageVersion}';
            }
            if (parameter === 3379) {
              return ${persona.webgl.maxTextureSize || 16384};
            }
            if (parameter === 34921) {
              return ${persona.webgl.maxVertexAttribs || 16};
            }
            if (parameter === 36348) {
              return ${persona.webgl.maxVaryingVectors || 30};
            }
            if (parameter === 33901) {
              return new Float32Array([${persona.webgl.aliasedLineWidthRange ? persona.webgl.aliasedLineWidthRange.join(',') : '1,1'}]);
            }
            if (parameter === 33902) {
              return new Float32Array([${persona.webgl.aliasedPointSizeRange ? persona.webgl.aliasedPointSizeRange.join(',') : '1,1024'}]);
            }
            return getParameter2.call(this, parameter);
          };
        }

        // Spoof WebGL extensions
        const getSupportedExtensions = WebGLRenderingContext.prototype.getSupportedExtensions;
        WebGLRenderingContext.prototype.getSupportedExtensions = function() {
          const extensions = getSupportedExtensions.call(this);
          // Add realistic extensions based on GPU vendor
          const vendorExtensions = '${persona.webgl.vendor}'.includes('NVIDIA') ? [
            'WEBGL_debug_renderer_info',
            'WEBGL_debug_shaders',
            'WEBGL_lose_context',
            'OES_element_index_uint',
            'OES_standard_derivatives',
            'OES_vertex_array_object',
            'WEBGL_compressed_texture_s3tc'
          ] : [
            'WEBGL_debug_renderer_info',
            'WEBGL_debug_shaders',
            'WEBGL_lose_context',
            'OES_element_index_uint',
            'OES_standard_derivatives',
            'OES_vertex_array_object'
          ];
          return [...new Set([...extensions, ...vendorExtensions])];
        };
      })();

      // Canvas Fingerprinting Protection (Enhanced)
      (function() {
        const toDataURL = HTMLCanvasElement.prototype.toDataURL;
        const getImageData = CanvasRenderingContext2D.prototype.getImageData;

        HTMLCanvasElement.prototype.toDataURL = function() {
          const result = toDataURL.apply(this, arguments);
          // Add subtle noise based on persona
          const noise = Math.sin(${persona.canvas.noiseLevel} * Math.PI) * 0.0001;
          return result.replace(/data:image\\/png;base64,/, 'data:image/png;base64,' + btoa(String.fromCharCode(Math.floor(noise * 255))).slice(0, 4));
        };

        CanvasRenderingContext2D.prototype.getImageData = function() {
          const result = getImageData.apply(this, arguments);
          // Add minimal noise to image data
          const data = result.data;
          for (let i = 0; i < data.length; i += 4) {
            if (Math.random() < ${persona.canvas.noiseLevel}) {
              data[i] = Math.min(255, data[i] + Math.floor(Math.random() * 3) - 1);
              data[i + 1] = Math.min(255, data[i + 1] + Math.floor(Math.random() * 3) - 1);
              data[i + 2] = Math.min(255, data[i + 2] + Math.floor(Math.random() * 3) - 1);
            }
          }
          return result;
        };
      })();

      // Screen properties spoofing
      (function() {
        Object.defineProperty(screen, 'width', {
          get: () => ${persona.screen.width},
          configurable: true
        });
        Object.defineProperty(screen, 'height', {
          get: () => ${persona.screen.height},
          configurable: true
        });
        Object.defineProperty(screen, 'availWidth', {
          get: () => ${persona.screen.availWidth},
          configurable: true
        });
        Object.defineProperty(screen, 'availHeight', {
          get: () => ${persona.screen.availHeight},
          configurable: true
        });
        Object.defineProperty(screen, 'colorDepth', {
          get: () => ${persona.screen.colorDepth},
          configurable: true
        });
        Object.defineProperty(screen, 'pixelDepth', {
          get: () => ${persona.screen.pixelDepth},
          configurable: true
        });
      })();

      // Plugins and mimeTypes spoofing
      (function() {
        const plugins = [
          { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
          { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' },
          { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }
        ];

        Object.defineProperty(navigator, 'plugins', {
          get: () => plugins,
          configurable: true
        });

        const mimeTypes = [
          { type: 'application/pdf', suffixes: 'pdf', description: 'Portable Document Format', enabledPlugin: plugins[0] },
          { type: 'text/pdf', suffixes: 'pdf', description: 'Portable Document Format', enabledPlugin: plugins[0] }
        ];

        Object.defineProperty(navigator, 'mimeTypes', {
          get: () => mimeTypes,
          configurable: true
        });
      })();

      // AudioContext fingerprinting protection
      (function() {
        if (window.AudioContext || window.webkitAudioContext) {
          const AudioContext = window.AudioContext || window.webkitAudioContext;
          const originalCreateAnalyser = AudioContext.prototype.createAnalyser;

          AudioContext.prototype.createAnalyser = function() {
            const analyser = originalCreateAnalyser.call(this);
            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;

            analyser.getFloatFrequencyData = function(array) {
              originalGetFloatFrequencyData.call(this, array);
              // Add minimal noise to audio fingerprint
              for (let i = 0; i < array.length; i++) {
                array[i] += (Math.random() - 0.5) * 0.0001;
              }
            };

            return analyser;
          };
        }
      })();

      // Permissions API spoofing
      (function() {
        if (navigator.permissions && navigator.permissions.query) {
          const originalQuery = navigator.permissions.query;
          navigator.permissions.query = function(permissionDesc) {
            return originalQuery.call(this, permissionDesc).then(result => {
              // Modify certain permissions to appear more natural
              if (permissionDesc.name === 'notifications') {
                Object.defineProperty(result, 'state', { value: 'default', configurable: true });
              }
              return result;
            });
          };
        }
      })();

      // Battery API removal (privacy concern)
      (function() {
        if (navigator.getBattery) {
          Object.defineProperty(navigator, 'getBattery', {
            get: () => undefined,
            configurable: true
          });
        }
        delete navigator.battery;
      })();

      // Connection API spoofing
      (function() {
        if (navigator.connection) {
          Object.defineProperty(navigator.connection, 'effectiveType', {
            get: () => '${persona.network ? persona.network.effectiveType : '4g'}',
            configurable: true
          });
          Object.defineProperty(navigator.connection, 'downlink', {
            get: () => ${persona.network ? persona.network.downlink : 10},
            configurable: true
          });
          Object.defineProperty(navigator.connection, 'rtt', {
            get: () => ${persona.network ? persona.network.rtt : 50},
            configurable: true
          });
        }
      })();

      // === CRITICAL: WEBRTC LEAK PROTECTION ===
      (function() {
        // Method 1: Completely disable WebRTC APIs
        if (window.RTCPeerConnection) {
          window.RTCPeerConnection = function() {
            throw new Error('WebRTC is disabled for privacy');
          };
        }

        if (window.webkitRTCPeerConnection) {
          window.webkitRTCPeerConnection = function() {
            throw new Error('WebRTC is disabled for privacy');
          };
        }

        if (window.mozRTCPeerConnection) {
          window.mozRTCPeerConnection = function() {
            throw new Error('WebRTC is disabled for privacy');
          };
        }

        // Method 2: Block getUserMedia completely
        if (navigator.mediaDevices) {
          navigator.mediaDevices.getUserMedia = function() {
            return Promise.reject(new Error('Permission denied'));
          };

          navigator.mediaDevices.enumerateDevices = function() {
            return Promise.resolve([]);
          };
        }

        if (navigator.getUserMedia) {
          navigator.getUserMedia = function(constraints, success, error) {
            if (error) error(new Error('Permission denied'));
          };
        }

        if (navigator.webkitGetUserMedia) {
          navigator.webkitGetUserMedia = function(constraints, success, error) {
            if (error) error(new Error('Permission denied'));
          };
        }

        if (navigator.mozGetUserMedia) {
          navigator.mozGetUserMedia = function(constraints, success, error) {
            if (error) error(new Error('Permission denied'));
          };
        }

        // Method 3: Remove WebRTC from global scope
        delete window.RTCPeerConnection;
        delete window.webkitRTCPeerConnection;
        delete window.mozRTCPeerConnection;
        delete window.RTCSessionDescription;
        delete window.RTCIceCandidate;
        delete window.RTCDataChannel;

        console.log('🔒 WebRTC completely disabled for maximum privacy');
      })();

      // Timezone spoofing
      (function() {
        const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
        Date.prototype.getTimezoneOffset = function() {
          // Calculate offset based on persona timezone
          return originalGetTimezoneOffset.call(this);
        };

        // Spoof Intl.DateTimeFormat
        if (window.Intl && window.Intl.DateTimeFormat) {
          const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
          Intl.DateTimeFormat.prototype.resolvedOptions = function() {
            const options = originalResolvedOptions.call(this);
            options.timeZone = '${persona.timezone}';
            return options;
          };
        }
      })();

      // === COMPREHENSIVE AUTOMATION DETECTION BYPASS ===
      (function() {
        // Remove ALL known automation detection properties
        const automationIndicators = [
          // Chrome DevTools Protocol
          'cdc_adoQpoasnfa76pfcZLmcfl_Array', 'cdc_adoQpoasnfa76pfcZLmcfl_Promise',
          'cdc_adoQpoasnfa76pfcZLmcfl_Symbol', 'cdc_adoQpoasnfa76pfcZLmcfl_JSON',
          'cdc_adoQpoasnfa76pfcZLmcfl_Object', 'cdc_adoQpoasnfa76pfcZLmcfl_Proxy',
          'cdc_adoQpoasnfa76pfcZLmcfl_Function', 'cdc_adoQpoasnfa76pfcZLmcfl_String',

          // Selenium indicators
          '_selenium', '__selenium_evaluate', '__selenium_unwrapped',
          '__webdriver_script_fn', '__webdriver_evaluate', '__webdriver_unwrapped',
          '__fxdriver_evaluate', '__fxdriver_unwrapped', '__driver_evaluate',
          '__driver_unwrapped', 'selenium', 'webdriver', 'driver',

          // PhantomJS indicators
          'callPhantom', '_phantom', '__phantom', 'phantom',

          // Nightmare indicators
          '__nightmare', 'nightmare',

          // Puppeteer indicators
          '__puppeteer_evaluation_script__', '_puppeteer',

          // Playwright indicators
          '__playwright', '_playwright', '__pw_evaluate',

          // Other automation tools
          'domAutomation', 'domAutomationController', '_WEBDRIVER_ELEM_CACHE',
          'ChromeDriverw', 'webdriver-evaluate', 'selenium-evaluate',
          'webdriverCommand', 'webdriver-evaluate-response', '__webdriverFunc',
          '__webdriver_script_function', '__$webdriverAsyncExecutor',
          '__lastWatirAlert', '__lastWatirConfirm', '__lastWatirPrompt',

          // Browser automation detection
          'geb', 'awesomium', 'emit', '__webdriver_script_fn',
          'spawn', 'webdriver', '__webdriver_evaluate', '__selenium_evaluate',
          '__webdriver_script_function', '__webdriver_unwrapped',
          '__selenium_unwrapped', '__fxdriver_evaluate', '__fxdriver_unwrapped',
          '__driver_evaluate', '__driver_unwrapped'
        ];

        // Remove all automation indicators
        automationIndicators.forEach(indicator => {
          try {
            delete window[indicator];
            delete document[indicator];
            delete navigator[indicator];
            if (window.top && window.top !== window) {
              delete window.top[indicator];
            }
            if (window.parent && window.parent !== window) {
              delete window.parent[indicator];
            }
          } catch (e) {}
        });

        // Override Object.defineProperty to prevent re-definition of automation properties
        const originalDefineProperty = Object.defineProperty;
        Object.defineProperty = function(obj, prop, descriptor) {
          if (automationIndicators.includes(prop)) {
            return obj;
          }
          return originalDefineProperty.call(this, obj, prop, descriptor);
        };

        // Override document.documentElement.getAttribute to hide automation attributes
        if (document.documentElement && document.documentElement.getAttribute) {
          const originalGetAttribute = document.documentElement.getAttribute;
          document.documentElement.getAttribute = function(name) {
            if (name === 'webdriver' || name === 'selenium' || name === 'driver') {
              return null;
            }
            return originalGetAttribute.call(this, name);
          };
        }
      })();

      // === TIKTOK-SPECIFIC EVASION TECHNIQUES (2025) ===
      (function() {
        // TikTok video player optimization
        const originalFetch = window.fetch;
        window.fetch = function(input, init) {
          const url = typeof input === 'string' ? input : input.url;

          // Add realistic delays for TikTok API calls
          if (url && (url.includes('tiktok') || url.includes('musical.ly') || url.includes('byteoversea'))) {
            return new Promise((resolve) => {
              setTimeout(() => {
                resolve(originalFetch.apply(this, arguments));
              }, Math.random() * 100 + 50);
            });
          }

          return originalFetch.apply(this, arguments);
        };

        // Override XMLHttpRequest for TikTok
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
          if (typeof url === 'string' &&
              (url.includes('tiktok') || url.includes('musical.ly') || url.includes('byteoversea'))) {

            this.addEventListener('readystatechange', function() {
              if (this.readyState === 1) {
                // Add TikTok-specific headers
                this.setRequestHeader('Accept', 'application/json, text/plain, */*');
                this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');
                this.setRequestHeader('Cache-Control', 'no-cache');
                this.setRequestHeader('Sec-Fetch-Dest', 'empty');
                this.setRequestHeader('Sec-Fetch-Mode', 'cors');
                this.setRequestHeader('Sec-Fetch-Site', 'same-origin');
              }
            });
          }
          return originalXHROpen.call(this, method, url, async, user, password);
        };

        // TikTok video element enhancement
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
          const element = originalCreateElement.call(this, tagName);

          if (tagName.toLowerCase() === 'video') {
            // Make video elements appear more natural
            Object.defineProperty(element, 'webkitDisplayingFullscreen', {
              get: () => false,
              configurable: true
            });

            Object.defineProperty(element, 'webkitSupportsFullscreen', {
              get: () => true,
              configurable: true
            });
          }

          return element;
        };

        // Bypass TikTok's bot detection
        if (window.byted_acrawler) {
          Object.defineProperty(window, 'byted_acrawler', {
            get: function() { return undefined; },
            configurable: true
          });
        }

        // Override TikTok's tracking functions
        if (window.ttq) {
          const originalTtq = window.ttq;
          window.ttq = function() {
            // Add delay to tracking calls
            setTimeout(() => originalTtq.apply(this, arguments), Math.random() * 50 + 25);
          };
        }
      })();

        // Override iframe detection
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
          const element = originalCreateElement.call(this, tagName);
          if (tagName.toLowerCase() === 'iframe') {
            // Add realistic iframe properties
            Object.defineProperty(element, 'contentWindow', {
              get: function() {
                return {
                  location: { href: 'about:blank' },
                  document: { readyState: 'complete' }
                };
              }
            });
          }
          return element;
        };

        // Spoof Google Analytics detection
        if (window.gtag) {
          const originalGtag = window.gtag;
          window.gtag = function() {
            // Add delay to analytics calls
            setTimeout(() => originalGtag.apply(this, arguments), Math.random() * 50 + 25);
          };
        }

        // Enhanced Google API detection bypass
        const enhanceGoogleAPIs = () => {
          // Bypass Google's advanced fingerprinting
          if (window.PerformanceObserver) {
            const originalObserve = PerformanceObserver.prototype.observe;
            PerformanceObserver.prototype.observe = function(options) {
              // Filter out automation-related performance entries
              if (options && options.entryTypes) {
                options.entryTypes = options.entryTypes.filter(type =>
                  !['navigation', 'resource'].includes(type)
                );
              }
              return originalObserve.call(this, options);
            };
          }

          // Bypass Google's timing attack detection
          if (window.performance && window.performance.now) {
            const originalNow = window.performance.now;
            let timeOffset = Math.random() * 10;
            window.performance.now = function() {
              return originalNow.call(this) + timeOffset;
            };
          }
            });
          }
          return originalFetch.call(this, url, options);
        };

        // Spoof mouse and keyboard events to appear more human
        const addHumanBehavior = () => {
          // Add subtle mouse movements
          let mouseX = 0, mouseY = 0;
          document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
          });

          // Simulate natural mouse movements
          setInterval(() => {
            if (Math.random() < 0.1) { // 10% chance every interval
              const event = new MouseEvent('mousemove', {
                clientX: mouseX + (Math.random() - 0.5) * 2,
                clientY: mouseY + (Math.random() - 0.5) * 2,
                bubbles: true
              });
              document.dispatchEvent(event);
            }
          }, 1000 + Math.random() * 2000);
        };

        // Initialize human behavior simulation
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', addHumanBehavior);
        } else {
          addHumanBehavior();
        }

        // Override performance.now() to add realistic timing
        const originalPerformanceNow = performance.now;
        performance.now = function() {
          return originalPerformanceNow.call(this) + Math.random() * 0.1;
        };

        // Spoof requestAnimationFrame timing
        const originalRAF = window.requestAnimationFrame;
        window.requestAnimationFrame = function(callback) {
          return originalRAF.call(this, function(timestamp) {
            // Add slight timing variation
            callback(timestamp + Math.random() * 0.5);
          });
        };
      })();

      console.log('🎭 Enhanced Antidetect script with Google evasion loaded for persona: ${persona.id}');
    `;
  }

  /**
   * Tạo browser với approach mới để loại bỏ hoàn toàn automation banner
   * @param {Object} persona - Persona object
   * @param {Object} proxy - Proxy object (optional)
   * @param {string} profileDir - Profile directory path
   * @returns {Promise<BrowserContext>} - Browser context
   */
  async createStealthBrowser(persona, proxy = null, profileDir = './stealth-profile') {
    const { chromium } = require('playwright');

    try {
      // Method 1: Try with system Chrome
      console.log('🔧 Attempting Method 1: System Chrome with stealth flags...');

      const launchOptions = {
        headless: false,
        devtools: false,
        executablePath: this.getSystemChromePath(),
        ignoreDefaultArgs: [
          '--enable-automation',
          '--enable-blink-features=AutomationControlled'
        ],
        args: [
          '--disable-blink-features=AutomationControlled',
          '--exclude-switches=enable-automation',
          '--disable-automation',
          '--disable-infobars',
          '--disable-dev-shm-usage',
          '--test-type',
          '--no-default-browser-check',
          '--no-first-run',
          '--disable-default-apps',
          '--disable-extensions',
          '--disable-component-extensions-with-background-pages',
          '--disable-background-extensions',
          '--disable-extension-activity-logging',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=TranslateUI',
          '--disable-features=BlinkGenPropertyTrees',
          '--disable-ipc-flooding-protection',
          '--disable-background-networking',
          '--disable-sync',
          '--disable-component-update',
          '--disable-domain-reliability',
          '--disable-logging',
          '--silent',
          '--log-level=3'
        ]
      };

      const contextOptions = await this.createTikTokContextOptions(persona, proxy);

      const browser = await chromium.launchPersistentContext(profileDir, {
        ...launchOptions,
        ...contextOptions
      });

      // Apply enhanced spoofing
      const spoofingScript = this.createUltimateStealthScript(persona);
      await browser.addInitScript(spoofingScript);

      console.log('✅ Method 1 successful: System Chrome launched');
      return browser;

    } catch (error) {
      console.log(`⚠️ Method 1 failed: ${error.message}`);

      try {
        // Method 2: Fallback to Playwright's Chromium
        console.log('🔧 Attempting Method 2: Playwright Chromium with enhanced stealth...');

        const fallbackOptions = this.createAlternativeBrowserLaunchOptions();
        const contextOptions = await this.createTikTokContextOptions(persona, proxy);

        const browser = await chromium.launchPersistentContext(profileDir, {
          ...fallbackOptions,
          ...contextOptions
        });

        // Apply ultimate spoofing
        const spoofingScript = this.createUltimateStealthScript(persona);
        await browser.addInitScript(spoofingScript);

        console.log('✅ Method 2 successful: Playwright Chromium launched');
        return browser;

      } catch (fallbackError) {
        console.error(`❌ Both methods failed: ${fallbackError.message}`);
        throw fallbackError;
      }
    }
  }

  /**
   * Get system Chrome executable path
   * @returns {string} - Chrome executable path
   */
  getSystemChromePath() {
    const os = require('os');
    const fs = require('fs');

    const paths = {
      darwin: [
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/Applications/Chromium.app/Contents/MacOS/Chromium'
      ],
      win32: [
        'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'
      ],
      linux: [
        '/usr/bin/google-chrome',
        '/usr/bin/google-chrome-stable',
        '/usr/bin/chromium-browser',
        '/usr/bin/chromium'
      ]
    };

    const platformPaths = paths[process.platform] || paths.linux;

    for (const path of platformPaths) {
      try {
        if (fs.existsSync(path)) {
          return path;
        }
      } catch (e) {
        // Continue to next path
      }
    }

    // Return undefined to use Playwright's bundled Chromium
    return undefined;
  }

  /**
   * Tạo minimal stealth script cho Native Chrome
   * @returns {String} - Minimal JavaScript code để inject
   */
  createNativeChromeStealthScript() {
    return `
      // Minimal stealth - chỉ remove webdriver
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // WebRTC complete disable
      if (window.RTCPeerConnection) {
        window.RTCPeerConnection = function() {
          throw new Error('WebRTC is disabled for privacy');
        };
      }

      if (navigator.mediaDevices) {
        navigator.mediaDevices.getUserMedia = function() {
          return Promise.reject(new Error('Permission denied'));
        };
      }

      // Remove WebRTC from global scope
      delete window.RTCPeerConnection;
      delete window.webkitRTCPeerConnection;
      delete window.mozRTCPeerConnection;

      console.log('🔧 Native Chrome stealth + WebRTC protection applied');
    `;
  }

  /**
   * Tạo ultimate stealth script để loại bỏ hoàn toàn automation detection
   * @param {Object} persona - Persona object
   * @returns {string} - Ultimate stealth JavaScript code
   */
  createUltimateStealthScript(persona) {
    return `
      // === ULTIMATE AUTOMATION DETECTION BYPASS (2025) ===

      // 0. IMMEDIATE EXECUTION BEFORE ANY DETECTION
      (function() {
        'use strict';

        // === CRITICAL: REMOVE ALL AUTOMATION INDICATORS ===

        // 1. Complete webdriver removal
        try {
          delete Object.getPrototypeOf(navigator).webdriver;
          delete navigator.__proto__.webdriver;
          delete navigator.webdriver;
          delete window.webdriver;

          // Override with non-enumerable property
          Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            set: () => {},
            enumerable: false,
            configurable: false
          });
        } catch(e) {}

        // 2. Remove Chrome DevTools Protocol indicators
        const cdcProps = [
          'cdc_adoQpoasnfa76pfcZLmcfl_Array', 'cdc_adoQpoasnfa76pfcZLmcfl_Promise',
          'cdc_adoQpoasnfa76pfcZLmcfl_Symbol', 'cdc_adoQpoasnfa76pfcZLmcfl_JSON',
          'cdc_adoQpoasnfa76pfcZLmcfl_Object', 'cdc_adoQpoasnfa76pfcZLmcfl_Proxy'
        ];

        cdcProps.forEach(prop => {
          try {
            delete window[prop];
            Object.defineProperty(window, prop, {
              get: () => undefined,
              set: () => {},
              enumerable: false,
              configurable: false
            });
          } catch(e) {}
        });

        // 3. Remove Playwright indicators
        const playwrightProps = ['__playwright', '_playwright', '__pw_evaluate'];
        playwrightProps.forEach(prop => {
          try {
            delete window[prop];
            delete navigator[prop];
          } catch(e) {}
        });

        // 4. Override automation detection functions
        window.chrome = window.chrome || {};
        window.chrome.runtime = window.chrome.runtime || {
          onConnect: undefined,
          onMessage: undefined,
          connect: () => ({}),
          sendMessage: () => ({})
        };

        // 5. Fix permissions API to appear normal
        if (navigator.permissions && navigator.permissions.query) {
          const originalQuery = navigator.permissions.query;
          navigator.permissions.query = function(parameters) {
            if (parameters.name === 'notifications') {
              return Promise.resolve({ state: 'default' });
            }
            return originalQuery.apply(this, arguments);
          };
        }

        // 6. Override plugins to appear normal
        Object.defineProperty(navigator, 'plugins', {
          get: () => ({
            length: 3,
            0: { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer' },
            1: { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai' },
            2: { name: 'Native Client', filename: 'internal-nacl-plugin' }
          }),
          enumerable: true,
          configurable: false
        });

        // 7. Fix languages to match persona
        Object.defineProperty(navigator, 'languages', {
          get: () => ['${persona.language || 'en-US'}', 'en'],
          enumerable: true,
          configurable: false
        });

        // 8. Override hardwareConcurrency to realistic value
        Object.defineProperty(navigator, 'hardwareConcurrency', {
          get: () => ${persona.hardwareConcurrency || 8},
          enumerable: true,
          configurable: false
        });

        // 9. CRITICAL: WebRTC Complete Disable
        if (window.RTCPeerConnection) {
          window.RTCPeerConnection = function() {
            throw new Error('WebRTC is disabled for privacy');
          };
        }

        if (window.webkitRTCPeerConnection) {
          window.webkitRTCPeerConnection = function() {
            throw new Error('WebRTC is disabled for privacy');
          };
        }

        if (window.mozRTCPeerConnection) {
          window.mozRTCPeerConnection = function() {
            throw new Error('WebRTC is disabled for privacy');
          };
        }

        // Block getUserMedia completely
        if (navigator.mediaDevices) {
          navigator.mediaDevices.getUserMedia = function() {
            return Promise.reject(new Error('Permission denied'));
          };
          navigator.mediaDevices.enumerateDevices = function() {
            return Promise.resolve([]);
          };
        }

        if (navigator.getUserMedia) {
          navigator.getUserMedia = function(constraints, success, error) {
            if (error) error(new Error('Permission denied'));
          };
        }

        // Remove WebRTC from global scope
        delete window.RTCPeerConnection;
        delete window.webkitRTCPeerConnection;
        delete window.mozRTCPeerConnection;
        delete window.RTCSessionDescription;
        delete window.RTCIceCandidate;

        // 9. Fix deviceMemory
        Object.defineProperty(navigator, 'deviceMemory', {
          get: () => ${persona.deviceMemory || 8},
          enumerable: true,
          configurable: false
        });

        // 10. Override screen properties
        Object.defineProperty(screen, 'width', {
          get: () => ${persona.screenWidth || 1920},
          enumerable: true,
          configurable: false
        });

        Object.defineProperty(screen, 'height', {
          get: () => ${persona.screenHeight || 1080},
          enumerable: true,
          configurable: false
        });

        Object.defineProperty(screen, 'availWidth', {
          get: () => ${persona.screenWidth || 1920},
          enumerable: true,
          configurable: false
        });

        Object.defineProperty(screen, 'availHeight', {
          get: () => ${(persona.screenHeight || 1080) - 40},
          enumerable: true,
          configurable: false
        });

        // 11. Fix timezone
        try {
          if (Intl && Intl.DateTimeFormat) {
            const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
            Intl.DateTimeFormat.prototype.resolvedOptions = function() {
              const options = originalResolvedOptions.call(this);
              options.timeZone = '${persona.timezone || 'America/New_York'}';
              return options;
            };
          }
        } catch(e) {}

        // 12. Override Date.getTimezoneOffset
        const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
        Date.prototype.getTimezoneOffset = function() {
          return ${persona.timezoneOffset || -300}; // EST offset
        };

        console.log('🛡️ Phase 1: Critical automation indicators removed');
      })();
          set: () => {},
          enumerable: false,
          configurable: true
        });
      })();

      // 2. COMPREHENSIVE AUTOMATION PROPERTY REMOVAL
      (function() {
        const automationProps = [
          'webdriver', '__webdriver_evaluate', '__selenium_evaluate', '__webdriver_script_fn',
          '__webdriver_script_func', '__webdriver_script_function', '__webdriver_unwrapped',
          '__selenium_unwrapped', '__fxdriver_evaluate', '__fxdriver_unwrapped',
          '__driver_evaluate', '__driver_unwrapped', '_selenium', 'calledSelenium',
          '_WEBDRIVER_ELEM_CACHE', 'ChromeDriverw', 'driver-evaluate', 'webdriver-evaluate',
          'selenium-evaluate', 'webdriverCommand', 'webdriver-evaluate-response',
          '__webdriverFunc', '__lastWatirAlert', '__lastWatirConfirm', '__lastWatirPrompt',
          'cdc_adoQpoasnfa76pfcZLmcfl_Array', 'cdc_adoQpoasnfa76pfcZLmcfl_Promise',
          'cdc_adoQpoasnfa76pfcZLmcfl_Symbol', 'cdc_adoQpoasnfa76pfcZLmcfl_JSON',
          'cdc_adoQpoasnfa76pfcZLmcfl_Object', 'cdc_adoQpoasnfa76pfcZLmcfl_Proxy',
          'cdc_adoQpoasnfa76pfcZLmcfl_Function', 'cdc_adoQpoasnfa76pfcZLmcfl_String',
          'callPhantom', '_phantom', '__phantom', 'phantom', '__nightmare', 'nightmare',
          '__puppeteer_evaluation_script__', '_puppeteer', '__playwright', '_playwright',
          '__pw_evaluate', 'domAutomation', 'domAutomationController'
        ];

        // Remove from all possible locations
        automationProps.forEach(prop => {
          try {
            delete window[prop];
            delete document[prop];
            delete navigator[prop];
            delete Object.getPrototypeOf(navigator)[prop];
            delete Object.getPrototypeOf(window)[prop];
            delete Object.getPrototypeOf(document)[prop];

            // Override with undefined getter
            [window, document, navigator].forEach(obj => {
              try {
                Object.defineProperty(obj, prop, {
                  get: () => undefined,
                  set: () => {},
                  enumerable: false,
                  configurable: true
                });
              } catch (e) {}
            });
          } catch (e) {}
        });
      })();

      // 3. CHROME OBJECT ENHANCEMENT
      (function() {
        // Create ultra-realistic chrome object
        if (!window.chrome) {
          window.chrome = {};
        }

        // Enhanced runtime object
        Object.defineProperty(window.chrome, 'runtime', {
          value: {
            onConnect: undefined,
            onMessage: undefined,
            onStartup: undefined,
            onInstalled: undefined,
            onSuspend: undefined,
            onSuspendCanceled: undefined,
            onUpdateAvailable: undefined,
            onBrowserUpdateAvailable: undefined,
            onRestartRequired: undefined,
            onConnectExternal: undefined,
            onMessageExternal: undefined,
            getBackgroundPage: () => undefined,
            getManifest: () => ({
              name: 'Chrome',
              version: '122.0.6261.94',
              manifest_version: 3,
              description: 'The web browser from Google'
            }),
            getURL: (path) => 'chrome-extension://invalid/' + path,
            reload: () => {},
            requestUpdateCheck: () => Promise.resolve(['no_update', undefined]),
            restart: () => {},
            sendMessage: () => Promise.resolve(),
            sendNativeMessage: () => Promise.resolve(),
            setUninstallURL: () => {},
            openOptionsPage: () => Promise.resolve(),
            getPlatformInfo: () => Promise.resolve({
              os: '${persona.platform === 'Windows' ? 'win' : 'mac'}',
              arch: 'x86-64',
              nacl_arch: 'x86-64'
            }),
            id: undefined
          },
          writable: false,
          enumerable: true,
          configurable: false
        });

        // Enhanced loadTimes function
        window.chrome.loadTimes = () => ({
          requestTime: Date.now() / 1000 - Math.random() * 10,
          startLoadTime: Date.now() / 1000 - Math.random() * 8,
          commitLoadTime: Date.now() / 1000 - Math.random() * 6,
          finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 4,
          finishLoadTime: Date.now() / 1000 - Math.random() * 2,
          firstPaintTime: Date.now() / 1000 - Math.random(),
          firstPaintAfterLoadTime: 0,
          navigationType: 'Other',
          wasFetchedViaSpdy: false,
          wasNpnNegotiated: false,
          npnNegotiatedProtocol: '',
          wasAlternateProtocolAvailable: false,
          connectionInfo: 'http/1.1'
        });

        // Enhanced csi function
        window.chrome.csi = () => ({
          startE: Date.now() - Math.random() * 1000,
          onloadT: Date.now() - Math.random() * 500,
          pageT: Math.random() * 1000 + 1000,
          tran: 15
        });

        // App property
        window.chrome.app = {
          isInstalled: false,
          InstallState: {
            DISABLED: 'disabled',
            INSTALLED: 'installed',
            NOT_INSTALLED: 'not_installed'
          },
          RunningState: {
            CANNOT_RUN: 'cannot_run',
            READY_TO_RUN: 'ready_to_run',
            RUNNING: 'running'
          }
        };
      })();

      // === PHASE 3: ADVANCED FINGERPRINTING PROTECTION ===
      (function() {
        'use strict';

        // 1. Enhanced Canvas Fingerprinting Protection
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

        HTMLCanvasElement.prototype.toDataURL = function(type, quality) {
          const context = this.getContext('2d');
          if (context) {
            const imageData = context.getImageData(0, 0, this.width, this.height);
            const data = imageData.data;
            const seed = ${persona.canvasSeed || 12345};

            // Add deterministic but unique noise
            for (let i = 0; i < data.length; i += 4) {
              const noise = (Math.sin(seed + i) * 10000) % 1;
              data[i] = Math.min(255, Math.max(0, data[i] + noise));
              data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + noise));
              data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + noise));
            }

            context.putImageData(imageData, 0, 0);
          }
          return originalToDataURL.apply(this, arguments);
        };

        // 2. WebGL Fingerprinting Protection
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
          if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
            return '${persona.webgl.vendor}';
          }
          if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
            return '${persona.webgl.renderer}';
          }
          return originalGetParameter.apply(this, arguments);
        };

        // Also handle WebGL2
        if (window.WebGL2RenderingContext) {
          const originalGetParameter2 = WebGL2RenderingContext.prototype.getParameter;
          WebGL2RenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) return '${persona.webgl.vendor}';
            if (parameter === 37446) return '${persona.webgl.renderer}';
            return originalGetParameter2.apply(this, arguments);
          };
        }

        // 3. Audio Context Fingerprinting Protection
        if (window.AudioContext || window.webkitAudioContext) {
          const AudioContextClass = window.AudioContext || window.webkitAudioContext;
          const originalCreateAnalyser = AudioContextClass.prototype.createAnalyser;

          AudioContextClass.prototype.createAnalyser = function() {
            const analyser = originalCreateAnalyser.apply(this, arguments);
            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;

            analyser.getFloatFrequencyData = function(array) {
              originalGetFloatFrequencyData.apply(this, arguments);
              for (let i = 0; i < array.length; i++) {
                array[i] += (Math.random() - 0.5) * 0.0001;
              }
            };

            return analyser;
          };
        }

        // 4. Performance API Spoofing
        if (window.performance && window.performance.now) {
          const originalNow = window.performance.now;
          let startTime = originalNow.call(window.performance);

          window.performance.now = function() {
            return originalNow.call(this) - startTime + (Math.random() - 0.5) * 0.1;
          };
        }

        // 5. Enhanced Navigator Properties
        Object.defineProperty(navigator, 'userAgent', {
          get: () => '${persona.userAgent}',
          enumerable: true,
          configurable: false
        });

        Object.defineProperty(navigator, 'platform', {
          get: () => '${persona.platform}',
          enumerable: true,
          configurable: false
        });

        Object.defineProperty(navigator, 'vendor', {
          get: () => 'Google Inc.',
          enumerable: true,
          configurable: false
        });

        // 6. Battery API Removal
        if (navigator.getBattery) {
          navigator.getBattery = undefined;
        }

        // 7. Enhanced Permissions API
        if (navigator.permissions && navigator.permissions.query) {
          const originalQuery = navigator.permissions.query;
          navigator.permissions.query = function(parameters) {
            const permissionStates = {
              'notifications': 'default',
              'geolocation': 'prompt',
              'camera': 'prompt',
              'microphone': 'prompt'
            };

            const state = permissionStates[parameters.name] || 'prompt';
            return Promise.resolve({ state: state });
          };
        }

        // 8. Connection API Spoofing
        if (navigator.connection) {
          Object.defineProperty(navigator.connection, 'effectiveType', {
            get: () => '4g',
            enumerable: true,
            configurable: false
          });

          Object.defineProperty(navigator.connection, 'downlink', {
            get: () => 10,
            enumerable: true,
            configurable: false
          });
        }

        console.log('🛡️ Phase 3: Advanced fingerprinting protection applied');
      })();

      // === PHASE 4: MOUSE AND KEYBOARD EVENT NORMALIZATION ===
      (function() {
        'use strict';

        // 1. Mouse Event Enhancement
        const originalDispatchEvent = EventTarget.prototype.dispatchEvent;
        EventTarget.prototype.dispatchEvent = function(event) {
          if (event instanceof MouseEvent) {
            // Add realistic timing variance
            const delay = Math.random() * 2;
            if (delay > 1) {
              setTimeout(() => originalDispatchEvent.call(this, event), delay);
              return true;
            }
          }
          return originalDispatchEvent.call(this, event);
        };

        // 2. Keyboard Event Enhancement
        const originalKeyboardEvent = window.KeyboardEvent;
        window.KeyboardEvent = function(type, eventInitDict) {
          if (eventInitDict) {
            // Add realistic timing
            eventInitDict.timeStamp = Date.now() + Math.random() * 10;
          }
          return new originalKeyboardEvent(type, eventInitDict);
        };

        // 3. Touch Event Support (for mobile simulation)
        if (!window.TouchEvent && persona.isMobile) {
          window.TouchEvent = function(type, eventInitDict) {
            const event = new Event(type, eventInitDict);
            event.touches = eventInitDict?.touches || [];
            event.targetTouches = eventInitDict?.targetTouches || [];
            event.changedTouches = eventInitDict?.changedTouches || [];
            return event;
          };
        }

        console.log('🛡️ Phase 4: Event normalization applied');
      })();

      console.log('🛡️ Ultimate stealth script loaded - automation detection bypassed');
    `;
  }

  /**
   * Tạo TikTok-specific context options (Enhanced 2025)
   * @param {Object} persona - Persona object
   * @param {Object} proxy - Proxy object (optional)
   * @param {string} accountId - Account ID for bridge management (optional)
   * @returns {Promise<Object>} - Enhanced context options for TikTok
   */
  async createTikTokContextOptions(persona, proxy = null, accountId = null) {
    const baseOptions = await this.createContextOptions(persona, proxy, accountId);

    // TikTok-specific enhancements
    return {
      ...baseOptions,
      javaScriptEnabled: true,
      bypassCSP: false, // TikTok requires CSP compliance
      ignoreHTTPSErrors: false, // TikTok requires HTTPS validation
      acceptDownloads: true,
      extraHTTPHeaders: {
        ...baseOptions.extraHTTPHeaders,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'max-age=0',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'sec-ch-ua': `"Not_A Brand";v="8", "Chromium";v="${this.extractChromeVersion(persona.userAgent)}", "Google Chrome";v="${this.extractChromeVersion(persona.userAgent)}"`,
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': `"${persona.platform === 'Windows' ? 'Windows' : 'macOS'}"`,
        'X-Forwarded-For': proxy ? proxy.ip : undefined,
        'CF-Connecting-IP': proxy ? proxy.ip : undefined,
        'X-Real-IP': proxy ? proxy.ip : undefined
      },
      // TikTok video optimization - remove invalid options
      // Enhanced permissions for TikTok features
      permissions: ['geolocation', 'camera', 'microphone', 'notifications'],
      // Media settings for video playback
      colorScheme: 'light',
      reducedMotion: 'no-preference',
      forcedColors: 'none'
    };
  }

  /**
   * Tạo Google-specific context options
   * @param {Object} persona - Persona object
   * @param {Object} proxy - Proxy object (optional)
   * @param {string} accountId - Account ID for bridge management (optional)
   * @returns {Promise<Object>} - Enhanced context options for Google services
   */
  async createGoogleContextOptions(persona, proxy = null, accountId = null) {
    const baseOptions = await this.createContextOptions(persona, proxy, accountId);

    // Add Google-specific enhancements
    return {
      ...baseOptions,
      javaScriptEnabled: true,
      bypassCSP: true,
      ignoreHTTPSErrors: true,
      extraHTTPHeaders: {
        ...baseOptions.extraHTTPHeaders,
        'Sec-GPC': '1',
        'X-Forwarded-For': proxy ? proxy.ip : undefined,
        'CF-Connecting-IP': proxy ? proxy.ip : undefined,
        'X-Real-IP': proxy ? proxy.ip : undefined
      }
    };
  }

  /**
   * Tạo browser launch options NATIVE CHROME cho TikTok Video (VERIFIED WORKING)
   * @returns {Object} - Native Chrome options that work with TikTok videos
   */
  createTikTokNativeChromeOptions() {
    return {
      headless: false,
      devtools: false,
      // Sử dụng Chrome thật - CRITICAL cho video playback
      executablePath: process.platform === 'darwin'
        ? '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        : process.platform === 'win32'
        ? 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'
        : '/usr/bin/google-chrome',
      ignoreDefaultArgs: [
        '--enable-automation',
        '--enable-blink-features=AutomationControlled'
      ],
      args: [
        // Minimal stealth - chỉ những gì cần thiết
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--no-default-browser-check',
        '--no-first-run',

        // Video optimization - VERIFIED working
        '--autoplay-policy=no-user-gesture-required',
        '--enable-accelerated-video-decode',
        '--use-gl=desktop',

        // WebRTC leak protection
        '--disable-webrtc-multiple-routes',
        '--disable-webrtc-hw-decoding',
        '--disable-webrtc-hw-encoding',
        '--force-webrtc-ip-handling-policy=disable_non_proxied_udp',

        // Security minimal
        '--no-sandbox',
        '--disable-setuid-sandbox',

        // Logging minimal
        '--disable-logging',
        '--silent',
        '--log-level=3'
      ]
    };
  }

  /**
   * Tạo TikTok browser với VERIFIED working configuration
   * @param {Object} persona - Persona object
   * @param {Object} proxy - Proxy configuration
   * @returns {Object} - Browser instance
   */
  async createTikTokVideoWorkingBrowser(persona, proxy = null) {
    console.log('🎯 Creating TikTok browser with VERIFIED working config...');

    try {
      const launchOptions = this.createTikTokNativeChromeOptions();
      const contextOptions = await this.createTikTokContextOptions(persona, proxy);

      const browser = await chromium.launchPersistentContext(
        `./profiles/${persona.id}`,
        {
          ...launchOptions,
          ...contextOptions
        }
      );

      // Apply minimal stealth
      const stealthScript = this.createNativeChromeStealthScript();
      await browser.addInitScript(stealthScript);

      console.log('✅ TikTok video browser created successfully');
      return browser;

    } catch (error) {
      console.error('❌ Failed to create TikTok video browser:', error.message);
      throw error;
    }
  }

  /**
   * Navigate to optimal TikTok URL for video content
   * @param {Object} page - Playwright page object
   * @returns {Promise} - Navigation promise
   */
  async navigateToTikTokOptimal(page) {
    console.log('🌐 Navigating to optimal TikTok URL...');

    // Try foryou page first (has more videos)
    try {
      await page.goto('https://www.tiktok.com/foryou', {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });
      console.log('✅ Navigated to TikTok For You page');
      return;
    } catch (error) {
      console.log('⚠️  For You page failed, trying explore...');

      // Fallback to explore page
      await page.goto('https://www.tiktok.com/explore', {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });
      console.log('✅ Navigated to TikTok Explore page');
    }
  }

  /**
   * Tạo browser launch options tối ưu cho TikTok Video Playback (FIXED)
   * @returns {Object} - Browser launch options optimized for video
   */
  createTikTokVideoOptimizedLaunchOptions() {
    return {
      headless: false,
      devtools: false,
      ignoreDefaultArgs: ['--enable-automation', '--enable-blink-features=AutomationControlled'],
      args: [
        // === AUTOMATION BANNER REMOVAL (MINIMAL) ===
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--test-type',
        '--no-default-browser-check',
        '--no-first-run',
        '--disable-default-apps',

        // === CRITICAL: ALLOW MEDIA LOADING ===
        '--allow-running-insecure-content',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-site-isolation-trials',
        '--disable-features=BlockInsecurePrivateNetworkRequests',

        // === VIDEO PLAYBACK OPTIMIZATION ===
        '--autoplay-policy=no-user-gesture-required',
        '--enable-accelerated-video-decode',
        '--enable-accelerated-video-encode',
        '--enable-gpu-rasterization',
        '--enable-zero-copy',
        '--use-gl=desktop',
        '--enable-webgl',
        '--enable-webgl2',
        '--enable-accelerated-2d-canvas',
        '--disable-software-rasterizer',

        // === MEDIA CODEC SUPPORT ===
        '--enable-features=VaapiVideoDecoder',
        '--enable-features=VaapiVideoEncoder',
        '--enable-features=WebRTC-H264WithOpenH264FFmpeg',
        '--enable-features=AudioServiceOutOfProcess',
        '--enable-features=MediaFoundationVideoCapture',
        '--enable-accelerated-jpeg-decoding',
        '--enable-accelerated-mjpeg-decode',

        // === NETWORK OPTIMIZATION ===
        '--aggressive-cache-discard',
        '--enable-tcp-fast-open',
        '--enable-experimental-web-platform-features',
        '--enable-features=NetworkService',
        '--enable-features=NetworkServiceLogging',

        // === PERFORMANCE ===
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-ipc-flooding-protection',
        '--disable-background-media-suspend',

        // === MINIMAL PRIVACY FLAGS ===
        '--disable-sync',
        '--disable-extensions',
        '--disable-component-update',
        '--disable-domain-reliability',
        '--disable-logging',
        '--silent',
        '--log-level=3',

        // === MEMORY OPTIMIZATION ===
        '--max_old_space_size=4096',
        '--memory-pressure-off'
      ]
    };
  }

  /**
   * Tạo browser launch options tối ưu cho TikTok (ULTIMATE No Automation Banner)
   * @returns {Object} - Browser launch options without automation detection
   */
  createTikTokBrowserLaunchOptions() {
    return {
      headless: false,
      devtools: false,
      // CRITICAL: Use executablePath to avoid automation detection
      executablePath: process.platform === 'darwin'
        ? '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        : process.platform === 'win32'
        ? 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'
        : '/usr/bin/google-chrome',
      args: [
        // === ULTIMATE AUTOMATION BANNER REMOVAL ===
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--disable-dev-shm-usage',
        '--test-type',
        '--no-default-browser-check',
        '--no-first-run',
        '--disable-default-apps',

        // === REMOVE ALL AUTOMATION INDICATORS ===
        '--disable-extensions-except',
        '--disable-extensions-file-access-check',
        '--disable-extensions-http-throttling',
        '--disable-component-extensions-with-background-pages',
        '--disable-background-extensions',
        '--disable-extension-activity-logging',
        '--disable-extensions',

        // === CRITICAL FLAGS FOR STEALTH ===
        '--user-agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
        '--disable-blink-features=AutomationControlled',
        '--disable-features=VizDisplayCompositor',
        '--disable-features=TranslateUI',
        '--disable-features=BlinkGenPropertyTrees',
        '--disable-features=UserAgentClientHint',
        '--disable-features=WebPayments',
        '--disable-features=WebUSB',
        '--disable-features=WebBluetooth',

        // === WEBRTC LEAK PROTECTION ===
        '--disable-webrtc-multiple-routes',
        '--disable-webrtc-hw-decoding',
        '--disable-webrtc-hw-encoding',
        '--force-webrtc-ip-handling-policy=disable_non_proxied_udp',
        '--webrtc-ip-handling-policy=disable_non_proxied_udp',

        // === TIKTOK VIDEO OPTIMIZATION ===
        '--autoplay-policy=no-user-gesture-required',
        '--disable-features=MediaFoundationVideoCapture',
        '--enable-features=VaapiVideoDecoder',
        '--use-gl=desktop',
        '--enable-gpu-rasterization',
        '--enable-zero-copy',
        '--enable-hardware-overlays',

        // === PERFORMANCE & STABILITY ===
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-ipc-flooding-protection',
        '--disable-background-networking',
        '--disable-field-trial-config',
        '--disable-back-forward-cache',

        // === PRIVACY & TRACKING BYPASS ===
        '--disable-sync',
        '--disable-component-update',
        '--disable-background-downloads',
        '--disable-domain-reliability',
        '--disable-client-side-phishing-detection',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-breakpad',
        '--disable-crash-reporter',
        '--disable-logging',
        '--silent',
        '--log-level=3',
        '--disable-metrics',
        '--disable-metrics-reporting',

        // === MEMORY OPTIMIZATION ===
        '--max_old_space_size=4096',
        '--memory-pressure-off',
        '--aggressive-cache-discard',
        '--disable-background-media-suspend'
      ]
    };
  }

  /**
   * Tạo browser launch options với approach khác (fallback)
   * @returns {Object} - Alternative browser launch options
   */
  createAlternativeBrowserLaunchOptions() {
    return {
      headless: false,
      devtools: false,
      ignoreDefaultArgs: [
        '--enable-automation',
        '--enable-blink-features=AutomationControlled'
      ],
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-features=BlinkGenPropertyTrees',
        '--disable-ipc-flooding-protection'
      ]
    };
  }
}

module.exports = AntidetectManager;
