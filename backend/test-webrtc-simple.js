const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testWebRTCSimple() {
  console.log('🧪 Simple WebRTC Test on browserleaks.com...\n');
  
  const antidetectManager = new AntidetectManager();
  let browser = null;

  try {
    // Load persona
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.personas[0];
    console.log(`📋 Using persona: ${persona.id}`);

    // Create browser with maximum WebRTC protection
    const contextOptions = await antidetectManager.createTikTokContextOptions(persona);
    
    browser = await chromium.launchPersistentContext('./webrtc-simple-test', {
      headless: false,
      devtools: false,
      ignoreDefaultArgs: ['--enable-automation'],
      args: [
        // Stealth
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-infobars',
        '--test-type',
        
        // Maximum WebRTC blocking
        '--disable-webrtc',
        '--disable-webrtc-multiple-routes',
        '--disable-webrtc-hw-decoding',
        '--disable-webrtc-hw-encoding',
        '--force-webrtc-ip-handling-policy=disable_non_proxied_udp',
        '--webrtc-ip-handling-policy=disable_non_proxied_udp',
        '--disable-features=WebRtcHideLocalIpsWithMdns',
        '--disable-rtc-smoothness-algorithm',
        '--disable-webrtc-encryption',
        '--disable-webrtc-stun-origin',
        
        // Security
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-logging',
        '--silent'
      ],
      ...contextOptions
    });

    // Apply ultimate WebRTC blocking script
    await browser.addInitScript(() => {
      // Complete WebRTC disable
      console.log('🔒 Disabling WebRTC...');
      
      // Delete all WebRTC APIs
      delete window.RTCPeerConnection;
      delete window.webkitRTCPeerConnection;
      delete window.mozRTCPeerConnection;
      delete window.RTCSessionDescription;
      delete window.RTCIceCandidate;
      delete window.RTCDataChannel;
      delete window.RTCStatsReport;
      
      // Override with error functions
      window.RTCPeerConnection = function() {
        throw new Error('WebRTC disabled');
      };
      
      window.webkitRTCPeerConnection = function() {
        throw new Error('WebRTC disabled');
      };
      
      // Block getUserMedia
      if (navigator.mediaDevices) {
        navigator.mediaDevices.getUserMedia = () => Promise.reject(new Error('Permission denied'));
        navigator.mediaDevices.enumerateDevices = () => Promise.resolve([]);
      }
      
      if (navigator.getUserMedia) {
        navigator.getUserMedia = (constraints, success, error) => {
          if (error) error(new Error('Permission denied'));
        };
      }
      
      console.log('✅ WebRTC disabled');
    });

    const page = await browser.newPage();
    console.log('✅ Browser launched with WebRTC protection!');

    // Navigate to browserleaks.com
    console.log('🌐 Opening browserleaks.com WebRTC test...');
    await page.goto('https://browserleaks.com/webrtc', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    console.log('✅ Page loaded! Check the WebRTC test results.');
    console.log('   Expected: "WebRTC Leak Test ✔ No Leak"');
    
    // Wait for user to check
    console.log('\n⏳ Waiting 60 seconds for manual inspection...');
    console.log('   Press Ctrl+C to exit early if satisfied with results.');
    await page.waitForTimeout(60000);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
      console.log('\n🔧 Browser closed');
    }
  }
}

// Run the test
testWebRTCSimple().catch(console.error);
