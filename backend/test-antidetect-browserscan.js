#!/usr/bin/env node

/**
 * Test script để kiểm tra antidetect system với browserscan.net
 * Chạy: node test-antidetect-browserscan.js
 */

const AntidetectManager = require('./src/antidetect/antidetect-manager');
const path = require('path');

async function testAntidetectWithBrowserscan() {
  console.log('🧪 Testing Antidetect System with browserscan.net...\n');

  const antidetectManager = new AntidetectManager();
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    console.log('✅ Personas loaded successfully');

    // Get a random persona
    const randomPersona = await antidetectManager.selectRandomPersona();
    console.log(`🎭 Using persona: ${randomPersona.id} (${randomPersona.platform})`);
    console.log(`   User-Agent: ${randomPersona.userAgent.substring(0, 80)}...`);
    console.log(`   Screen: ${randomPersona.screenWidth}x${randomPersona.screenHeight}`);
    console.log(`   WebGL: ${randomPersona.webgl.vendor} - ${randomPersona.webgl.renderer}`);

    // Create profile directory
    const profileDir = path.join(__dirname, 'profiles', `test-${Date.now()}`);
    
    console.log('\n🚀 Launching browser with enhanced antidetect...');
    
    // Launch browser with antidetect
    const browser = await antidetectManager.createStealthBrowser(randomPersona, null, profileDir);
    
    if (!browser) {
      throw new Error('Failed to launch browser');
    }

    console.log('✅ Browser launched successfully');

    // Get the first page
    const pages = browser.pages();
    const page = pages.length > 0 ? pages[0] : await browser.newPage();

    console.log('\n🔍 Navigating to browserscan.net...');
    
    // Navigate to browserscan.net
    await page.goto('https://www.browserscan.net/', {
      waitUntil: 'networkidle',
      timeout: 30000
    });

    console.log('✅ Page loaded successfully');

    // Wait for scan to complete
    console.log('⏳ Waiting for scan to complete (30 seconds)...');
    await page.waitForTimeout(30000);

    // Take screenshot
    const screenshotPath = path.join(__dirname, 'test-results', `browserscan-${Date.now()}.png`);
    await page.screenshot({ 
      path: screenshotPath, 
      fullPage: true 
    });
    console.log(`📸 Screenshot saved: ${screenshotPath}`);

    // Extract key detection results
    try {
      const results = await page.evaluate(() => {
        const results = {};
        
        // Try to find bot detection result
        const botDetectionElements = document.querySelectorAll('*');
        for (let element of botDetectionElements) {
          const text = element.textContent || '';
          if (text.includes('Bot Detection')) {
            const parent = element.closest('div, section, article');
            if (parent) {
              results.botDetection = parent.textContent.trim();
            }
          }
        }

        // Get hardware info
        const hardwareSection = document.querySelector('[data-testid="hardware"], .hardware, #hardware');
        if (hardwareSection) {
          results.hardware = hardwareSection.textContent.trim();
        }

        // Get timezone info
        const timezoneSection = document.querySelector('[data-testid="timezone"], .timezone, #timezone');
        if (timezoneSection) {
          results.timezone = timezoneSection.textContent.trim();
        }

        // Get WebGL info
        const webglSection = document.querySelector('[data-testid="webgl"], .webgl, #webgl');
        if (webglSection) {
          results.webgl = webglSection.textContent.trim();
        }

        return results;
      });

      console.log('\n📊 Scan Results:');
      console.log('================');
      
      if (results.botDetection) {
        console.log('🤖 Bot Detection:', results.botDetection);
      }
      
      if (results.hardware) {
        console.log('💻 Hardware:', results.hardware.substring(0, 200) + '...');
      }
      
      if (results.timezone) {
        console.log('🌍 Timezone:', results.timezone.substring(0, 100) + '...');
      }
      
      if (results.webgl) {
        console.log('🎮 WebGL:', results.webgl.substring(0, 100) + '...');
      }

    } catch (error) {
      console.log('⚠️ Could not extract detailed results:', error.message);
    }

    // Keep browser open for manual inspection
    console.log('\n🔍 Browser will remain open for 60 seconds for manual inspection...');
    console.log('   Check the browser window to see the full scan results');
    console.log('   Press Ctrl+C to close early');
    
    await page.waitForTimeout(60000);

    // Close browser
    await browser.close();
    console.log('✅ Browser closed');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Test interrupted by user');
  process.exit(0);
});

// Run the test
if (require.main === module) {
  testAntidetectWithBrowserscan()
    .then(() => {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testAntidetectWithBrowserscan };
